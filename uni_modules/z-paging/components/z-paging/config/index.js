// z-paging全局配置文件，注意避免更新时此文件被覆盖，若被覆盖，可在此文件中右键->点击本地历史记录，找回覆盖前的配置

export default {
	// 是否开启自定义下拉刷新刷新结束回弹动画效果(收回后上下弹一下的动画)
	'refresher-end-bounce-enabled': false,
	// 下拉刷新时下拉到“松手立即刷新”状态时是否使手机短振动，默认为否
	'refresher-vibrate': true,
	// 自定义下拉刷新结束收回动画时间，单位为毫秒
	'refresher-complete-duration': 150,
	// refresher-threshold	设置自定义下拉刷新阈值，默认单位为px。
	'refresher-threshold': '80rpx',
	// 下拉刷新配置
	'refresher-img-style': {
		width: '50rpx',
		height: '50rpx'
	},
	'refresher-title-style': {
		color: '#333333',
		fontSize: '30rpx'
	},
	// 底部加载更多配置
	'loading-more-default-as-loading': true,
	'loading-more-loading-icon-type': "flower",
	'loading-more-custom-style': {
		height: '120rpx'
	},
	'loading-more-title-custom-style': {
		color: '#333333',
		fontSize: '30rpx'
	},
	'loading-more-loading-icon-custom-style': {
		width: '50rpx',
		height: '50rpx'
	}
}
<template>
	<view class="read-more">
		<view class="read-more__content" :style="{ 
					height: isLongContent && status === 'close' ? $u.addUnit(showHeight) : $u.addUnit(contentHeight)
				}" @tap="toggleReadMore">
			<view class="read-more__content__inner" ref="read-more__content__inner" :class="[elId]">
				<slot></slot>
			</view>
		</view>
	</view>
</template>

<script>
	// #ifdef APP-NVUE
	const dom = uni.requireNativePlugin('dom')
	// #endif

	export default {
		name: 'view-text-more',
		mixins: [uni.$u.mpMixin, uni.$u.mixin],
		props: {
			// 列表中展示的时候 使用
			show: {
				type: Boolean,
				default: false,
			},
			// 默认的显示占位高度
			showHeight: {
				type: [String, Number],
				default: 40
			},
			// 展开后是否显示"收起"按钮
			toggle: {
				type: Boolean,
				default: true
			},
			// 关闭时的提示文字
			closeText: {
				type: String,
				default: '收起'
			},
			// 展开时的提示文字
			openText: {
				type: String,
				default: '展开'
			},
			// 提示的文字颜色
			color: {
				type: String,
				default: '#6B2BFE'
			},
			// 提示文字的大小
			fontSize: {
				type: [String, Number],
				default: '30rpx'
			},
			// open和close事件时，将此参数返回在回调参数中
			name: {
				type: [String, Number],
				default: ''
			}
		},
		data() {
			return {
				isLongContent: false, // 是否需要隐藏一部分内容
				status: 'close', // 当前隐藏与显示的状态，close-收起状态，open-展开状态
				elId: uni.$u.guid(6), // 生成唯一class
				contentHeight: 40, // 内容高度  
			}
		},
		computed: {

		},
		watch: {
			show: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.init()
					}
				},
				immediate: true
			},
		},
		mounted() {
			// this.init() 
		},
		methods: {
			async init() {
				this.getContentHeight().then(height => {
					if (uni.$u.test.isEmpty(height)) {
						return
					}
					// 判断高度，如果真实内容高度大于占位高度，则显示收起与展开的控制按钮
					console.log("得到文本的高度", height)
					if (height > uni.$u.getPx(this.showHeight)) {
						this.isLongContent = true
						this.status = 'close'
					}
					this.contentHeight = height
				})
			},
			// 获取内容的高度
			async getContentHeight() {
				// 延时一定时间再获取节点
				await uni.$u.sleep(30)
				return new Promise(resolve => {
					// #ifndef APP-NVUE
					this.$uGetRect('.' + this.elId).then(res => {
						resolve(res.height)
					})
					// #endif

					// #ifdef APP-NVUE
					const ref = this.$refs['read-more__content__inner']
					dom.getComponentRect(ref, (res) => {
						resolve(res.size.height)
					})
					// #endif
				})
			},
			// 展开或者收起
			toggleReadMore() {
				this.status = this.status === 'close' ? 'open' : 'close'
				// 如果toggle为false，隐藏"收起"部分的内容
				if (this.toggle == false) this.isLongContent = false
				// 发出打开或者收齐的事件
				this.$emit(this.status, this.name)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.read-more {
		position: relative;

		&__content {
			overflow: hidden;
			color: #FFFFFF;
			font-size: 15px;
			text-align: left;
		}

		&__toggle {
			display: flex;
			justify-content: center;
			position: absolute;
			right: 0;
			bottom: 0;

			&__inner {
				overflow: hidden;
			}

			&__text {
				display: flex;
				align-items: center;
				justify-content: center;
				// margin-top: 5px;
				background-color: #FFFFFF;
			}
		}
	}
</style>
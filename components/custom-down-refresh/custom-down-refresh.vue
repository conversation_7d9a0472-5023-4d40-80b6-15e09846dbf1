<!-- z-paging自定义的下拉刷新view -->
<template>
	<view class="refresher-container">
		<u-loading-icon mode="circle" timingFunction="linear" :text="statusText" :textColor="textColor">
		</u-loading-icon>
	</view>
</template>

<script>
	export default {
		name: 'custom-down-refresh',
		props: {
			// 刷新状态
			status: {
				type: Number,
				default: function() {
					return 0;
				},
			},
			// 文本颜色
			textColor: {
				type: String,
				default: function() {
					return '#FFFFFF';
				},
			}
		},
		data() {
			return {

			};
		},
		computed: {
			statusText() {
				// 这里可以做i18n国际化相关操作，可以通过uni.getLocale()获取当前语言(具体操作见i18n-demo.vue);
				// 获取到当前语言之后，就可以自定义不同语言下的展示内容了
				const statusTextArr = ['继续下拉刷新', '松手开始刷新', '正在努力刷新中...', '刷新成功'];
				return statusTextArr[this.status];
			}
		}
	}
</script>

<style scoped>
	.refresher-container {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		height: 80rpx;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
</style>
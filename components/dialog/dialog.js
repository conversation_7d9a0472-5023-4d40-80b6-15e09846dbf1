export default {
	/* 链接处理 */
	getLink(params) {
		let url = "/components/dialog/dialog";
		if (params) {
			let paramStr = "";
			for (let name in params) {
				paramStr += `&${name}=${params[name]}`
			}
			if (paramStr) {
				url += `?${paramStr.substr(1)}`
			}
		}
		return url;
	},
	/* APP全局弹窗 */
	dialog(params = {}, callback) {
		uni.navigateTo({
			url: this.getLink(params),
			success(e) {
				uni.$off("wujw_common_dialog");
				uni.$on("wujw_common_dialog", (type) => {
					callback && callback(type)
				})
			}
		})
	},
	/*弹出提示弹窗  */
	alert(data = {}, callback, close) {
		let params = {
			type: "alert",
			isCloseBtn: '0',
			isMaskClose: '0',
			...data
		};
		this.dialog(params, (type) => {
			if ("confirm" == type) {
				callback && callback()
			} else {
				close && close()
			}
		})
	},
	/*确认提示框弹窗 */
	confirm(data = {}, confirm, cancel, close) {
		let params = {
			type: "confirm",
			isCloseBtn: '0',
			isMaskClose: '0',
			...data
		};
		this.dialog(params, (type) => {
			if ("confirm" == type) {
				confirm && confirm()
			} else if ("cancel" == type) {
				cancel && cancel()
			} else if ("close" == type) {
				close && close()
			}
		})
	},

}

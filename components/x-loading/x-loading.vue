<template>
	<view class="x-loading">
		<u-overlay :show="loadingShow" :custom-style="overlayStyle">
			<view class="x-loading__content" :style="[contentStyle]" :class="['x-loading__content--loading']">
				<u-loading-icon mode="circle" color="rgb(255, 255, 255)" inactiveColor="rgb(120, 120, 120)"
					size="25"></u-loading-icon>
				<text class="x-loading__content__text">加载中</text>
			</view>
		</u-overlay>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	export default {
		name: 'x-loading',
		computed: {
			...mapState(['loadingShow']),
			overlayStyle() {
				return {
					justifyContent: 'center',
					alignItems: 'center',
					display: 'flex',
					backgroundColor: 'rgba(0, 0, 0, 0)'
				};
			},
			contentStyle() {
				return {
					transform: 'translateY(0)'
				};
			}
		}
	};
</script>

<style lang="scss" scoped>
	.x-loading {
		z-index: 999999999;
		&__content {
			display: flex;
			padding: 12px 20px;
			border-radius: 10px;
			background-color: #585858;
			color: #fff;
			align-items: center;
			max-width: 600rpx;
			position: relative;

			&--loading {
				flex-direction: column;
				padding: 20px 20px;
			}

			&__text {
				margin-top: 10px;
				color: #fff;
				font-size: 15px;
				line-height: 15px;
			}
		}
	}
</style>
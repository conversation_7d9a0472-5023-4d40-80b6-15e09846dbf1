<template>
	<view v-show="show && canShow">
		<view id="_drag_button" class="drag" :style="'left: ' + left + 'px; top:' + top + 'px;'"
			@touchstart="touchstart" @touchmove.stop.prevent="touchmove" @touchend="touchend"
			@click.stop.prevent="click" :class="{transition: isDock && !isMove }">
			<slot></slot>
			<view v-if="badgeNumber > 0" class="badge">{{badgeNumber >= 100 ? '99+' : badgeNumber }}</view>
		</view>

	</view>
</template>

<script>
	export default {
		name: 'drag-button',
		props: {
			//是否展示
			show: {
				type: Boolean,
				default: true
			},
			// 是否启用自动停靠
			isDock: {
				type: Boolean,
				default: false
			},
			// 当前页面是否包含tabbar
			existTabBar: {
				type: Boolean,
				default: false
			},
			// 消息红点
			badgeNumber: {
				type: Number,
				default: 0
			},
			// 初始展示距离底部边距
			marginBottom: {
				type: Number,
				default: 0
			},
			// 初始展示距离右边边距
			marginRight: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				top: 0,
				left: 0,
				width: 0,
				height: 0,
				offsetWidth: 0,
				offsetHeight: 0,
				windowWidth: 0,
				windowHeight: 0,
				isMove: true,
				edge: 10,
				
				canShow:false
			}
		},
		watch: {
			show: {
				handler(newName, oldName) {
					if (newName) {
						this.calculationPosition();
					}
				},
				immediate: true
			}
		},
		mounted() {
			const sys = uni.getSystemInfoSync();

			this.windowWidth = sys.windowWidth;
			this.windowHeight = sys.windowHeight;

			// #ifdef APP-PLUS
			this.existTabBar && (this.windowHeight -= 50);
			// #endif
			if (sys.windowTop) {
				this.windowHeight += sys.windowTop;
			}
			this.calculationPosition();
		},
		methods: {
			// 点击时会触发
			click() {
				this.$emit('btnClick');
			},
			calculationPosition() {
				const _that = this;
				_that.$nextTick(() => {
					const query = uni.createSelectorQuery().in(_that);
					query.select('#_drag_button').boundingClientRect(data => {
						_that.width = data.width == 0 ? 40 : data.width;
						_that.height = data.height == 0 ? 40 : data.height;
						_that.offsetWidth = _that.width / 2;
						_that.offsetHeight = _that.height / 2;
						_that.left = _that.windowWidth - _that.width - _that.edge - _that.marginRight;
						_that.top = _that.windowHeight - _that.height - _that.edge - _that.marginBottom;
						// 计算完位置之后才可以显示，否则会有闪动
						_that.canShow = true
					}).exec();
				})
			},
			// 拖拽开始时会触发
			touchstart(e) {
				this.$emit('btnTouchStart');
			},
			touchmove(e) {
				// 单指触摸
				if (e.touches.length !== 1) {
					return false;
				}

				this.isMove = true;

				this.left = e.touches[0].clientX - this.offsetWidth;

				let clientY = e.touches[0].clientY - this.offsetHeight;
				// #ifdef H5
				clientY += this.height;
				// #endif
				let edgeBottom = this.windowHeight - this.height - this.edge - this.marginBottom;

				// 上下触及边界
				if (clientY < this.edge) {
					this.top = this.edge;
				} else if (clientY > edgeBottom) {
					this.top = edgeBottom;
				} else {
					this.top = clientY
				}

			},
			// 拖拽结束时会触发
			touchend(e) {
				if (this.isDock) {
					let edgeRigth = this.windowWidth - this.width - this.edge - this.marginRight;

					if (this.left < this.windowWidth / 2 - this.offsetWidth) {
						this.left = this.edge;
					} else {
						this.left = edgeRigth;
					}
				}
				this.isMove = false;
				this.$emit('btnTouchend');
			},
		}
	}
</script>

<style lang="scss">
	.drag {
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.5);
		box-shadow: 0 0 6upx rgba(0, 0, 0, 0.4);
		color: $uni-text-color-inverse;
		width: 80upx;
		height: 80upx;
		border-radius: 50%;
		font-size: $uni-font-size-sm;
		position: fixed;
		z-index: 999;

		&.transition {
			transition: left .3s ease, top .3s ease;
		}

		.badge {
			padding: 2rpx 8rpx;
			position: absolute;
			top: -20rpx;
			right: -20rpx;
			border-radius: 20rpx;
			border: 2rpx solid #FFFFFF;
			background-color: red;
			color: #FFFFFF;
			font-size: 18rpx;
		}
	}
</style>
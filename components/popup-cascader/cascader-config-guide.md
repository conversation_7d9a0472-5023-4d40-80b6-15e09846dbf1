# 级联选择器配置对象使用指南

## 📋 配置对象结构

```javascript
cascaderPopup: {
  show: false,                    // 弹窗显示状态
  title: '请选择',                // 弹窗标题
  level: 2,                       // 级联层级数量
  multiple: false,                // 是否支持多选
  max: 1,                        // 最大选择数量（多选时有效）
  displayMode: 'list',           // 显示模式：list | grid
  cityOptions: [],               // 数据源
  modelValue: [],                // 选中值
  cascaderProps: {               // 字段映射配置
    value: 'value',
    label: 'label',
    children: 'children',
    disabled: 'disabled'
  },
  complete: null                 // 完成回调函数
}
```

## 🔧 配置项详解

### 1. show（弹窗显示状态）
- **类型**：`Boolean`
- **默认值**：`false`
- **说明**：控制弹窗的显示和隐藏

```javascript
// 显示弹窗
this.cascaderPopup.show = true;

// 隐藏弹窗
this.cascaderPopup.show = false;
```

### 2. title（弹窗标题）
- **类型**：`String`
- **默认值**：`'请选择'`
- **说明**：弹窗顶部显示的标题文字

```javascript
this.cascaderPopup.title = '选择城市';
this.cascaderPopup.title = '选择部门';
this.cascaderPopup.title = '选择商品分类';
```

### 3. level（级联层级数量）
- **类型**：`Number`
- **默认值**：`2`
- **范围**：`1-10`
- **说明**：级联选择的层级深度

```javascript
this.cascaderPopup.level = 1;  // 单级选择
this.cascaderPopup.level = 2;  // 省市选择
this.cascaderPopup.level = 3;  // 省市区选择
this.cascaderPopup.level = 4;  // 省市区街道选择
```

### 4. multiple（是否支持多选）
- **类型**：`Boolean`
- **默认值**：`false`
- **说明**：是否允许选择多个项目

```javascript
this.cascaderPopup.multiple = false;  // 单选模式
this.cascaderPopup.multiple = true;   // 多选模式
```

### 5. max（最大选择数量）
- **类型**：`Number`
- **默认值**：`1`
- **说明**：多选模式下的最大选择数量

```javascript
this.cascaderPopup.max = 1;   // 最多选择1个
this.cascaderPopup.max = 3;   // 最多选择3个
this.cascaderPopup.max = 10;  // 最多选择10个
```

### 6. displayMode（显示模式）
- **类型**：`String`
- **默认值**：`'list'`
- **可选值**：`'list'` | `'grid'`
- **说明**：最后一级的显示模式

```javascript
this.cascaderPopup.displayMode = 'list';  // 列表模式
this.cascaderPopup.displayMode = 'grid';  // 网格模式
```

### 7. cityOptions（数据源）
- **类型**：`Array`
- **默认值**：`[]`
- **说明**：级联选择的数据源

```javascript
this.cascaderPopup.cityOptions = [
  {
    value: '110000',
    label: '北京市',
    children: [
      {
        value: '110100',
        label: '北京市',
        children: [
          { value: '110101', label: '东城区' },
          { value: '110102', label: '西城区' }
        ]
      }
    ]
  }
];
```

### 8. modelValue（选中值）
- **类型**：`Array`
- **默认值**：`[]`
- **说明**：当前选中的值列表

```javascript
// 单选
this.cascaderPopup.modelValue = ['110101'];

// 多选
this.cascaderPopup.modelValue = ['110101', '310101'];

// 路径数组
this.cascaderPopup.modelValue = [['110000', '110100', '110101']];
```

### 9. cascaderProps（字段映射配置）
- **类型**：`Object`
- **说明**：自定义数据字段映射

```javascript
// 默认配置
this.cascaderPopup.cascaderProps = {
  value: 'value',      // 值字段
  label: 'label',      // 标签字段
  children: 'children', // 子项字段
  disabled: 'disabled'  // 禁用字段
};

// 自定义配置
this.cascaderPopup.cascaderProps = {
  value: 'id',
  label: 'name',
  children: 'subItems',
  disabled: 'isDisabled'
};
```

### 10. complete（完成回调函数）
- **类型**：`Function`
- **说明**：选择完成后的回调函数

```javascript
this.cascaderPopup.complete = (result) => {
  console.log('选择结果：', result);
  // 处理选择结果
  this.handleResult(result);
};
```

## 🎯 使用场景示例

### 场景1：城市选择（单选）
```javascript
showCitySelector() {
  this.cascaderPopup = {
    show: true,
    title: '选择城市',
    level: 3,
    multiple: false,
    max: 1,
    displayMode: 'list',
    cityOptions: this.cityData,
    modelValue: this.selectedCity,
    cascaderProps: {
      value: 'value',
      label: 'label',
      children: 'children',
      disabled: 'disabled'
    },
    complete: (result) => {
      this.selectedCity = result.value.map(item => item.value);
    }
  };
}
```

### 场景2：多城市选择
```javascript
showMultipleCitySelector() {
  this.cascaderPopup = {
    show: true,
    title: '选择多个城市',
    level: 3,
    multiple: true,
    max: 5,
    displayMode: 'list',
    cityOptions: this.cityData,
    modelValue: this.selectedCities,
    cascaderProps: {
      value: 'value',
      label: 'label',
      children: 'children',
      disabled: 'disabled'
    },
    complete: (result) => {
      this.selectedCities = result.value.map(item => item.value);
    }
  };
}
```

### 场景3：部门选择（2级）
```javascript
showDepartmentSelector() {
  this.cascaderPopup = {
    show: true,
    title: '选择部门',
    level: 2,
    multiple: false,
    max: 1,
    displayMode: 'list',
    cityOptions: this.departmentData,
    modelValue: this.selectedDepartment,
    cascaderProps: {
      value: 'id',
      label: 'name',
      children: 'children',
      disabled: 'disabled'
    },
    complete: (result) => {
      this.selectedDepartment = result.value.map(item => item.id);
    }
  };
}
```

### 场景4：商品分类（网格模式）
```javascript
showCategorySelector() {
  this.cascaderPopup = {
    show: true,
    title: '选择商品分类',
    level: 3,
    multiple: false,
    max: 1,
    displayMode: 'grid',
    cityOptions: this.categoryData,
    modelValue: this.selectedCategory,
    cascaderProps: {
      value: 'code',
      label: 'title',
      children: 'subCategories',
      disabled: 'isDisabled'
    },
    complete: (result) => {
      this.selectedCategory = result.value.map(item => item.code);
    }
  };
}
```

## 📝 回调结果格式

```javascript
{
  ident: 'cascader',           // 组件标识
  value: [                     // 选中的项目数组
    {
      value: '110101',         // 选中项的值
      label: '东城区',         // 选中项的标签
      path: ['110000', '110100', '110101'],        // 完整路径值
      pathLabels: ['北京市', '北京市', '东城区'],   // 完整路径标签
      level: 2                 // 选中项的层级
    }
  ],
  paths: [                     // 路径数组
    ['110000', '110100', '110101']  // 完整路径或仅末级值
  ]
}
```

## ⚠️ 注意事项

1. **数据格式**：确保数据源格式正确，包含必要的字段
2. **层级限制**：建议层级不超过5级，以保证性能和用户体验
3. **回调处理**：在 complete 回调中正确处理选择结果
4. **状态管理**：及时更新 modelValue 以保证数据同步
5. **错误处理**：添加适当的错误处理和用户提示

<template>
	<view class="simple-usage">
		<!-- 触发按钮 -->
		<view class="trigger-btn" @click="openCascader">
			{{ displayText || '点击选择' }}
		</view>
		
		<!-- 级联选择器弹窗 -->
		<u-popup v-model="cascaderPopup.show" mode="bottom" border-radius="20" :safe-area-inset-bottom="false">
			<popup-cascader 
				:show="cascaderPopup.show" 
				:title="cascaderPopup.title" 
				:level="cascaderPopup.level"
				:multiple="cascaderPopup.multiple" 
				:max="cascaderPopup.max" 
				:displayMode="cascaderPopup.displayMode"
				:options="cascaderPopup.cityOptions" 
				:modelValue="cascaderPopup.modelValue" 
				:props="cascaderPopup.cascaderProps"
				:complete="cascaderPopup.complete" 
				@close="cascaderPopup.show = false"
			/>
		</u-popup>
	</view>
</template>

<script>
import PopupCascader from './popup-cascader.vue';

export default {
	name: 'SimpleUsage',
	components: {
		PopupCascader
	},
	data() {
		return {
			// 选择结果
			selectedResult: [],
			
			// 级联选择器配置对象
			cascaderPopup: {
				show: false,                    // 弹窗显示状态
				title: '请选择城市',             // 弹窗标题
				level: 3,                       // 级联层级
				multiple: false,                // 是否多选
				max: 1,                        // 最大选择数量
				displayMode: 'list',           // 显示模式：list | grid
				cityOptions: [],               // 数据源
				modelValue: [],                // 选中值
				cascaderProps: {               // 字段映射配置
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: null                 // 完成回调函数
			}
		};
	},
	
	computed: {
		// 显示文本
		displayText() {
			if (this.selectedResult.length === 0) return '';
			
			if (this.cascaderPopup.multiple) {
				return `已选择 ${this.selectedResult.length} 项`;
			} else {
				const item = this.selectedResult[0];
				return item.pathLabels ? item.pathLabels.join(' / ') : item.label;
			}
		}
	},
	
	created() {
		// 初始化数据
		this.initData();
	},
	
	methods: {
		// 初始化数据
		initData() {
			// 模拟城市数据
			this.cascaderPopup.cityOptions = [
				{
					value: '110000',
					label: '北京市',
					children: [
						{
							value: '110100',
							label: '北京市',
							children: [
								{ value: '110101', label: '东城区' },
								{ value: '110102', label: '西城区' },
								{ value: '110105', label: '朝阳区' },
								{ value: '110106', label: '丰台区' }
							]
						}
					]
				},
				{
					value: '310000',
					label: '上海市',
					children: [
						{
							value: '310100',
							label: '上海市',
							children: [
								{ value: '310101', label: '黄浦区' },
								{ value: '310104', label: '徐汇区' },
								{ value: '310105', label: '长宁区' }
							]
						}
					]
				},
				{
					value: '440000',
					label: '广东省',
					children: [
						{
							value: '440100',
							label: '广州市',
							children: [
								{ value: '440103', label: '荔湾区' },
								{ value: '440104', label: '越秀区' }
							]
						},
						{
							value: '440300',
							label: '深圳市',
							children: [
								{ value: '440303', label: '罗湖区' },
								{ value: '440304', label: '福田区' }
							]
						}
					]
				}
			];
			
			// 设置完成回调
			this.cascaderPopup.complete = this.onCascaderComplete;
		},
		
		// 打开级联选择器
		openCascader() {
			// 设置当前选中值
			this.cascaderPopup.modelValue = this.selectedResult.map(item => item.value);
			// 显示弹窗
			this.cascaderPopup.show = true;
		},
		
		// 级联选择完成回调
		onCascaderComplete(result) {
			console.log('选择结果：', result);
			
			// 保存选择结果
			this.selectedResult = result.value;
			
			// 可以在这里进行其他操作，比如：
			// 1. 发送请求保存数据
			// 2. 更新其他相关数据
			// 3. 触发其他业务逻辑
			
			// 显示成功提示
			uni.showToast({
				title: '选择成功',
				icon: 'success'
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.simple-usage {
	padding: 30rpx;
}

.trigger-btn {
	height: 80rpx;
	padding: 0 30rpx;
	background: #409eff;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	color: #ffffff;
	font-weight: 500;
	
	&:active {
		background: #3a8ee6;
	}
}
</style>

# ElCascaderPopup 级联选择器

一个功能强大的 UniApp 级联选择器组件，融合了 `popup-multiple-city` 和 `popup-link-select` 的所有功能，采用 Element Plus 的设计理念和命名规范。

## 特性

- 🎯 **Element Plus 风格**：采用 Element Plus 的设计理念和命名规范
- 🔄 **灵活配置**：支持单选/多选、一级/多级联动
- 📱 **多种模式**：支持列表和网格两种显示模式
- 🎨 **自定义样式**：可配置的字段映射和样式
- 🔍 **智能回显**：支持末级编号传入，自动构建完整路径
- 📊 **完整路径**：可选择返回完整路径或仅返回值
- ⚡ **高性能**：优化的数据处理和渲染逻辑

## 基本用法

```vue
<template>
  <u-popup v-model="show" mode="bottom">
    <el-cascader-popup
      :show="show"
      title="选择地区"
      :level="3"
      :multiple="false"
      :options="cityOptions"
      :modelValue="selectedValue"
      @confirm="onConfirm"
      @close="onClose"
    />
  </u-popup>
</template>

<script>
import ElCascaderPopup from '@/components/popup-cascader/popup-cascader.vue'

export default {
  components: { ElCascaderPopup },
  data() {
    return {
      show: false,
      selectedValue: [],
      cityOptions: [
        {
          id: '110000',
          name: '北京市',
          children: [
            {
              id: '110100', 
              name: '北京市',
              children: [
                { id: '110101', name: '东城区' },
                { id: '110102', name: '西城区' }
              ]
            }
          ]
        }
      ]
    }
  },
  methods: {
    onConfirm(result) {
      console.log('选择结果：', result)
      this.selectedValue = result.value
    },
    onClose() {
      this.show = false
    }
  }
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| ident | 弹窗标识 | String | 'cascader' |
| show | 弹窗状态 | Boolean | false |
| title | 标题 | String | '请选择' |
| level | 级联层级数量 | Number | 2 |
| multiple | 是否多选 | Boolean | false |
| max | 最大选择数量（多选时有效） | Number | 1 |
| displayMode | 显示模式：list \| grid | String | 'list' |
| options | 数据源 | Array | [] |
| props | 数据源配置 | Object | 见下方 |
| modelValue | 选中值 | Array | [] |
| checkStrictly | 是否只能选择叶子节点 | Boolean | true |
| emitPath | 是否发出完整路径 | Boolean | true |
| panelHeight | 面板高度（rpx） | Number | 800 |
| complete | 完成回调 | Function | - |

### Props 配置

```javascript
props: {
  value: 'value',      // 指定选项的值为选项对象的某个属性值
  label: 'label',      // 指定选项标签为选项对象的某个属性值
  children: 'children', // 指定选项的子选项为选项对象的某个属性值
  disabled: 'disabled'  // 指定选项的禁用为选项对象的某个属性值
}
```

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| confirm | 确认选择时触发 | result: { ident, value, paths } |
| close | 关闭弹窗时触发 | { ident } |

### 回调结果格式

```javascript
{
  ident: 'cascader',           // 弹窗标识
  value: [                     // 选中的项目数组
    {
      id: '110105',            // 选中项的值
      name: '朝阳区',          // 选中项的标签
      path: ['110000', '110100', '110105'],        // 完整路径值
      pathLabels: ['北京市', '北京市', '朝阳区'],   // 完整路径标签
      level: 2                 // 选中项的层级
    }
  ],
  paths: [                     // 路径数组（根据 emitPath 配置）
    ['110000', '110100', '110105']  // 完整路径或仅末级值
  ]
}
```

## 使用场景

### 1. 单选模式

```vue
<el-cascader-popup
  :multiple="false"
  :max="1"
  :options="cityOptions"
  @confirm="onSingleConfirm"
/>
```

### 2. 多选模式

```vue
<el-cascader-popup
  :multiple="true"
  :max="3"
  :options="cityOptions"
  @confirm="onMultipleConfirm"
/>
```

### 3. 网格显示模式

```vue
<el-cascader-popup
  displayMode="grid"
  :options="cityOptions"
  @confirm="onGridConfirm"
/>
```

### 4. 末级编号回显

```vue
<el-cascader-popup
  :modelValue="['110105']"  <!-- 直接传入朝阳区编号 -->
  :options="cityOptions"
  @confirm="onEchoConfirm"
/>
```

### 5. 自定义字段映射

```vue
<el-cascader-popup
  :props="{
    value: 'code',
    label: 'title',
    children: 'subItems'
  }"
  :options="customOptions"
  @confirm="onCustomConfirm"
/>
```

## 数据格式

```javascript
const options = [
  {
    id: '110000',           // 值字段
    name: '北京市',         // 标签字段
    disabled: false,        // 禁用字段（可选）
    children: [             // 子项字段
      {
        id: '110100',
        name: '北京市',
        children: [
          { id: '110101', name: '东城区' },
          { id: '110102', name: '西城区' }
        ]
      }
    ]
  }
]
```

## 与原组件的对比

| 功能 | popup-multiple-city | popup-link-select | popup-cascader |
|------|---------------------|-------------------|----------------|
| 单选/多选 | 多选 | 单选/多选 | ✅ 单选/多选 |
| 级联层级 | 1-2级 | 1-2级 | ✅ 任意级 |
| 显示模式 | 列表/网格 | 列表 | ✅ 列表/网格 |
| 数据来源 | 接口获取 | Props传入 | ✅ Props传入 |
| 字段映射 | 固定 | 固定 | ✅ 可配置 |
| 末级回显 | ❌ | ❌ | ✅ 支持 |
| 路径返回 | 部分 | 部分 | ✅ 完整 |

## 注意事项

1. **数据结构**：确保数据源符合树形结构，每个节点包含必要的字段
2. **性能优化**：大数据量时建议分页加载或虚拟滚动
3. **样式定制**：可通过修改 SCSS 变量来定制主题色彩
4. **兼容性**：支持 UniApp 各平台，建议在真机上测试

## 更新日志

### v1.0.0
- 🎉 初始版本发布
- ✨ 融合 popup-multiple-city 和 popup-link-select 功能
- ✨ 支持末级编号回显
- ✨ 统一的 API 设计
- ✨ 完整的路径返回机制

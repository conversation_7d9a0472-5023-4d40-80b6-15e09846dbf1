<template>
	<view class="usage-example">
		<view class="demo-section">
			<text class="section-title">级联选择器使用示例</text>
			
			<!-- 触发按钮 -->
			<view class="trigger-buttons">
				<view class="trigger-btn" @click="showCitySelector">
					{{ selectedCityText || '选择城市' }}
				</view>
				
				<view class="trigger-btn" @click="showMultipleCitySelector">
					{{ selectedMultipleCityText || '选择多个城市' }}
				</view>
				
				<view class="trigger-btn" @click="showDepartmentSelector">
					{{ selectedDepartmentText || '选择部门' }}
				</view>
				
				<view class="trigger-btn" @click="showCategorySelector">
					{{ selectedCategoryText || '选择分类（网格模式）' }}
				</view>
			</view>
			
			<!-- 选择结果展示 -->
			<view class="result-section">
				<text class="result-title">选择结果：</text>
				<text class="result-content">{{ resultText }}</text>
			</view>
		</view>
		
		<!-- 级联选择器弹窗 -->
		<u-popup v-model="cascaderPopup.show" mode="bottom" border-radius="20" :safe-area-inset-bottom="false">
			<popup-cascader 
				:show="cascaderPopup.show" 
				:title="cascaderPopup.title" 
				:level="cascaderPopup.level"
				:multiple="cascaderPopup.multiple" 
				:max="cascaderPopup.max" 
				:displayMode="cascaderPopup.displayMode"
				:options="cascaderPopup.cityOptions" 
				:modelValue="cascaderPopup.modelValue" 
				:props="cascaderPopup.cascaderProps"
				:complete="cascaderPopup.complete" 
				@close="cascaderPopup.show = false"
			/>
		</u-popup>
	</view>
</template>

<script>
import PopupCascader from './popup-cascader.vue';

export default {
	name: 'UsageExample',
	components: {
		PopupCascader
	},
	data() {
		return {
			// 选择结果
			selectedCity: [],
			selectedMultipleCity: [],
			selectedDepartment: [],
			selectedCategory: [],
			
			// 级联选择器配置
			cascaderPopup: {
				show: false,
				title: '',
				level: 2,
				multiple: false,
				max: 1,
				displayMode: 'list',
				cityOptions: [],
				modelValue: [],
				cascaderProps: {
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: null
			},
			
			// 城市数据
			cityData: [
				{
					value: '110000',
					label: '北京市',
					children: [
						{
							value: '110100',
							label: '北京市',
							children: [
								{ value: '110101', label: '东城区' },
								{ value: '110102', label: '西城区' },
								{ value: '110105', label: '朝阳区' },
								{ value: '110106', label: '丰台区' }
							]
						}
					]
				},
				{
					value: '310000',
					label: '上海市',
					children: [
						{
							value: '310100',
							label: '上海市',
							children: [
								{ value: '310101', label: '黄浦区' },
								{ value: '310104', label: '徐汇区' },
								{ value: '310105', label: '长宁区' },
								{ value: '310106', label: '静安区' }
							]
						}
					]
				},
				{
					value: '440000',
					label: '广东省',
					children: [
						{
							value: '440100',
							label: '广州市',
							children: [
								{ value: '440103', label: '荔湾区' },
								{ value: '440104', label: '越秀区' },
								{ value: '440105', label: '海珠区' }
							]
						},
						{
							value: '440300',
							label: '深圳市',
							children: [
								{ value: '440303', label: '罗湖区' },
								{ value: '440304', label: '福田区' },
								{ value: '440305', label: '南山区' }
							]
						}
					]
				}
			],
			
			// 部门数据
			departmentData: [
				{
					value: 'tech',
					label: '技术部',
					children: [
						{ value: 'frontend', label: '前端组' },
						{ value: 'backend', label: '后端组' },
						{ value: 'mobile', label: '移动端组' }
					]
				},
				{
					value: 'product',
					label: '产品部',
					children: [
						{ value: 'pm', label: '产品经理' },
						{ value: 'ui', label: 'UI设计师' },
						{ value: 'ue', label: 'UE设计师' }
					]
				},
				{
					value: 'market',
					label: '市场部',
					children: [
						{ value: 'sales', label: '销售组' },
						{ value: 'operation', label: '运营组' }
					]
				}
			],
			
			// 分类数据
			categoryData: [
				{
					value: 'electronics',
					label: '电子产品',
					children: [
						{
							value: 'phone',
							label: '手机',
							children: [
								{ value: 'iphone', label: 'iPhone' },
								{ value: 'android', label: 'Android' },
								{ value: 'huawei', label: '华为' },
								{ value: 'xiaomi', label: '小米' }
							]
						},
						{
							value: 'computer',
							label: '电脑',
							children: [
								{ value: 'laptop', label: '笔记本' },
								{ value: 'desktop', label: '台式机' },
								{ value: 'tablet', label: '平板电脑' }
							]
						}
					]
				},
				{
					value: 'clothing',
					label: '服装',
					children: [
						{
							value: 'men',
							label: '男装',
							children: [
								{ value: 'shirt', label: '衬衫' },
								{ value: 'pants', label: '裤子' },
								{ value: 'jacket', label: '外套' }
							]
						},
						{
							value: 'women',
							label: '女装',
							children: [
								{ value: 'dress', label: '连衣裙' },
								{ value: 'skirt', label: '裙子' },
								{ value: 'blouse', label: '上衣' }
							]
						}
					]
				}
			]
		};
	},
	
	computed: {
		// 单选城市显示文本
		selectedCityText() {
			if (this.selectedCity.length === 0) return '';
			const city = this.selectedCity[0];
			return city.pathLabels ? city.pathLabels.join(' / ') : city.label;
		},
		
		// 多选城市显示文本
		selectedMultipleCityText() {
			if (this.selectedMultipleCity.length === 0) return '';
			return `已选择 ${this.selectedMultipleCity.length} 个城市`;
		},
		
		// 部门显示文本
		selectedDepartmentText() {
			if (this.selectedDepartment.length === 0) return '';
			const dept = this.selectedDepartment[0];
			return dept.pathLabels ? dept.pathLabels.join(' / ') : dept.label;
		},
		
		// 分类显示文本
		selectedCategoryText() {
			if (this.selectedCategory.length === 0) return '';
			const category = this.selectedCategory[0];
			return category.pathLabels ? category.pathLabels.join(' / ') : category.label;
		},
		
		// 结果展示文本
		resultText() {
			const results = [];
			if (this.selectedCity.length > 0) {
				results.push(`城市: ${this.selectedCityText}`);
			}
			if (this.selectedMultipleCity.length > 0) {
				const cities = this.selectedMultipleCity.map(city => 
					city.pathLabels ? city.pathLabels.join('/') : city.label
				).join(', ');
				results.push(`多选城市: ${cities}`);
			}
			if (this.selectedDepartment.length > 0) {
				results.push(`部门: ${this.selectedDepartmentText}`);
			}
			if (this.selectedCategory.length > 0) {
				results.push(`分类: ${this.selectedCategoryText}`);
			}
			return results.length > 0 ? results.join('\n') : '暂无选择';
		}
	},
	
	methods: {
		// 显示城市选择器（单选）
		showCitySelector() {
			this.cascaderPopup = {
				show: true,
				title: '选择城市',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				cityOptions: this.cityData,
				modelValue: this.selectedCity.map(item => item.value),
				cascaderProps: {
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: (result) => {
					console.log('城市选择结果：', result);
					this.selectedCity = result.value;
				}
			};
		},
		
		// 显示城市选择器（多选）
		showMultipleCitySelector() {
			this.cascaderPopup = {
				show: true,
				title: '选择多个城市',
				level: 3,
				multiple: true,
				max: 5,
				displayMode: 'list',
				cityOptions: this.cityData,
				modelValue: this.selectedMultipleCity.map(item => item.value),
				cascaderProps: {
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: (result) => {
					console.log('多选城市结果：', result);
					this.selectedMultipleCity = result.value;
				}
			};
		},
		
		// 显示部门选择器
		showDepartmentSelector() {
			this.cascaderPopup = {
				show: true,
				title: '选择部门',
				level: 2,
				multiple: false,
				max: 1,
				displayMode: 'list',
				cityOptions: this.departmentData,
				modelValue: this.selectedDepartment.map(item => item.value),
				cascaderProps: {
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: (result) => {
					console.log('部门选择结果：', result);
					this.selectedDepartment = result.value;
				}
			};
		},
		
		// 显示分类选择器（网格模式）
		showCategorySelector() {
			this.cascaderPopup = {
				show: true,
				title: '选择商品分类',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'grid',
				cityOptions: this.categoryData,
				modelValue: this.selectedCategory.map(item => item.value),
				cascaderProps: {
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: (result) => {
					console.log('分类选择结果：', result);
					this.selectedCategory = result.value;
				}
			};
		}
	}
};
</script>

<style lang="scss" scoped>
.usage-example {
	padding: 30rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.demo-section {
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 40rpx;
	text-align: center;
}

.trigger-buttons {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 40rpx;
}

.trigger-btn {
	height: 100rpx;
	padding: 0 30rpx;
	background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
	border-radius: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #ffffff;
	font-weight: 500;
	box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 8rpx rgba(64, 158, 255, 0.3);
	}
}

.result-section {
	padding: 30rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	border-left: 6rpx solid #409eff;
}

.result-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #409eff;
	margin-bottom: 15rpx;
}

.result-content {
	font-size: 26rpx;
	color: #606266;
	line-height: 1.6;
	white-space: pre-line;
}
</style>

<template>
	<view class="test-fix">
		<view class="test-section">
			<text class="section-title">修复测试</text>
			
			<!-- 单选测试（max=1，应该直接确认） -->
			<view class="test-item">
				<text class="test-title">单选测试（max=1，自动确认）</text>
				<view class="test-btn" @click="testSingleSelect">
					{{ singleResult || '点击测试单选' }}
				</view>
			</view>
			
			<!-- 单选测试（max>1，需要手动确认） -->
			<view class="test-item">
				<text class="test-title">单选测试（max=3，手动确认）</text>
				<view class="test-btn" @click="testSingleSelectManual">
					{{ singleManualResult || '点击测试单选手动确认' }}
				</view>
			</view>
			
			<!-- 多选测试 -->
			<view class="test-item">
				<text class="test-title">多选测试（max=3）</text>
				<view class="test-btn" @click="testMultipleSelect">
					{{ multipleResult || '点击测试多选' }}
				</view>
			</view>
			
			<!-- 数据回显测试 -->
			<view class="test-item">
				<text class="test-title">数据回显测试</text>
				<view class="test-btn" @click="testDataEcho">
					{{ echoResult || '点击测试数据回显' }}
				</view>
			</view>
		</view>
		
		<!-- 级联选择器弹窗 -->
		<u-popup v-model="cascaderPopup.show" mode="bottom" border-radius="20" :safe-area-inset-bottom="false">
			<popup-cascader 
				:show="cascaderPopup.show" 
				:title="cascaderPopup.title" 
				:level="cascaderPopup.level"
				:multiple="cascaderPopup.multiple" 
				:max="cascaderPopup.max" 
				:displayMode="cascaderPopup.displayMode"
				:options="cascaderPopup.cityOptions" 
				:modelValue="cascaderPopup.modelValue" 
				:props="cascaderPopup.cascaderProps"
				:complete="cascaderPopup.complete" 
				@close="cascaderPopup.show = false"
			/>
		</u-popup>
	</view>
</template>

<script>
import PopupCascader from './popup-cascader.vue';

export default {
	name: 'TestFix',
	components: {
		PopupCascader
	},
	data() {
		return {
			// 测试结果
			singleResult: '',
			singleManualResult: '',
			multipleResult: '',
			echoResult: '',
			
			// 级联选择器配置
			cascaderPopup: {
				show: false,
				title: '',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				cityOptions: [],
				modelValue: [],
				cascaderProps: {
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: null
			},
			
			// 测试数据
			testData: [
				{
					value: '110000',
					label: '北京市',
					children: [
						{
							value: '110100',
							label: '北京市',
							children: [
								{ value: '110101', label: '东城区' },
								{ value: '110102', label: '西城区' },
								{ value: '110105', label: '朝阳区' }
							]
						}
					]
				},
				{
					value: '310000',
					label: '上海市',
					children: [
						{
							value: '310100',
							label: '上海市',
							children: [
								{ value: '310101', label: '黄浦区' },
								{ value: '310104', label: '徐汇区' }
							]
						}
					]
				}
			]
		};
	},
	
	methods: {
		// 测试单选（max=1，自动确认）
		testSingleSelect() {
			console.log('=== 开始测试单选（自动确认）===');
			this.cascaderPopup = {
				show: true,
				title: '单选测试（自动确认）',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				cityOptions: this.testData,
				modelValue: [],
				cascaderProps: {
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: (result) => {
					console.log('单选（自动确认）结果：', result);
					if (result.value && result.value.length > 0) {
						const item = result.value[0];
						this.singleResult = item.pathLabels ? item.pathLabels.join(' / ') : item.label;
					}
				}
			};
		},
		
		// 测试单选（max>1，手动确认）
		testSingleSelectManual() {
			console.log('=== 开始测试单选（手动确认）===');
			this.cascaderPopup = {
				show: true,
				title: '单选测试（手动确认）',
				level: 3,
				multiple: false,
				max: 3,
				displayMode: 'list',
				cityOptions: this.testData,
				modelValue: [],
				cascaderProps: {
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: (result) => {
					console.log('单选（手动确认）结果：', result);
					if (result.value && result.value.length > 0) {
						const item = result.value[0];
						this.singleManualResult = item.pathLabels ? item.pathLabels.join(' / ') : item.label;
					}
				}
			};
		},
		
		// 测试多选
		testMultipleSelect() {
			console.log('=== 开始测试多选 ===');
			this.cascaderPopup = {
				show: true,
				title: '多选测试',
				level: 3,
				multiple: true,
				max: 3,
				displayMode: 'list',
				cityOptions: this.testData,
				modelValue: [],
				cascaderProps: {
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: (result) => {
					console.log('多选结果：', result);
					if (result.value && result.value.length > 0) {
						this.multipleResult = `已选择 ${result.value.length} 项`;
					}
				}
			};
		},
		
		// 测试数据回显
		testDataEcho() {
			console.log('=== 开始测试数据回显 ===');
			this.cascaderPopup = {
				show: true,
				title: '数据回显测试',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				cityOptions: this.testData,
				modelValue: ['110105'], // 预设朝阳区
				cascaderProps: {
					value: 'value',
					label: 'label',
					children: 'children',
					disabled: 'disabled'
				},
				complete: (result) => {
					console.log('数据回显结果：', result);
					if (result.value && result.value.length > 0) {
						const item = result.value[0];
						this.echoResult = item.pathLabels ? item.pathLabels.join(' / ') : item.label;
					}
				}
			};
		}
	}
};
</script>

<style lang="scss" scoped>
.test-fix {
	padding: 30rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.test-section {
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 40rpx;
	text-align: center;
}

.test-item {
	margin-bottom: 30rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.test-title {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 15rpx;
}

.test-btn {
	height: 80rpx;
	padding: 0 30rpx;
	background: #409eff;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	color: #ffffff;
	font-weight: 500;
	
	&:active {
		background: #3a8ee6;
	}
}
</style>

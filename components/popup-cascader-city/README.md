# ElCascaderCityPopup 城市级联选择器

一个集成了城市数据请求功能的 UniApp 级联选择器组件，采用 Element Plus 设计风格，自动从接口获取城市数据并提供缓存机制。

## 特性

- 🌍 **自动获取城市数据**：集成接口请求，自动获取城市数据
- 💾 **智能缓存机制**：自动缓存数据，减少网络请求
- 🎯 **Element Plus 风格**：采用 Element Plus 的设计理念和命名规范
- 🔄 **灵活配置**：支持单选/多选、1-5级联动
- 📱 **多种模式**：支持列表和网格两种显示模式
- 🔍 **智能回显**：支持末级编号传入，自动构建完整路径
- ⚡ **高性能**：优化的数据处理和渲染逻辑
- 🔄 **数据刷新**：支持强制刷新和缓存清除

## 基本用法

```vue
<template>
  <u-popup v-model="show" mode="bottom">
    <el-cascader-city-popup
      :show="show"
      title="选择城市"
      :level="3"
      :multiple="false"
      :modelValue="selectedValue"
      @confirm="onConfirm"
      @close="onClose"
    />
  </u-popup>
</template>

<script>
import ElCascaderCityPopup from '@/components/popup-cascader-city/popup-cascader-city.vue'

export default {
  components: { ElCascaderCityPopup },
  data() {
    return {
      show: false,
      selectedValue: []
    }
  },
  methods: {
    onConfirm(result) {
      console.log('选择结果：', result)
      this.selectedValue = result.value.map(item => item.id)
    },
    onClose() {
      this.show = false
    }
  }
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| ident | 弹窗标识 | String | 'cascader-city' |
| show | 弹窗状态 | Boolean | false |
| title | 标题 | String | '选择城市' |
| level | 级联层级数量（1-5级） | Number | 3 |
| multiple | 是否多选 | Boolean | false |
| max | 最大选择数量（多选时有效） | Number | 1 |
| displayMode | 显示模式：list \| grid | String | 'list' |
| props | 数据源配置 | Object | 见下方 |
| modelValue | 选中值 | Array | [] |
| checkStrictly | 是否只能选择叶子节点 | Boolean | true |
| emitPath | 是否发出完整路径 | Boolean | true |
| panelHeight | 面板高度（rpx） | Number | 800 |
| complete | 完成回调 | Function | - |
| apiUrl | 城市数据API地址 | String | '/system/area/tree' |
| cacheKey | 缓存键名 | String | 'a-citys-link-list' |
| forceRefresh | 是否强制刷新数据 | Boolean | false |

### Props 配置

```javascript
props: {
  value: 'id',         // 指定选项的值为选项对象的某个属性值
  label: 'name',       // 指定选项标签为选项对象的某个属性值
  children: 'children', // 指定选项的子选项为选项对象的某个属性值
  disabled: 'disabled'  // 指定选项的禁用为选项对象的某个属性值
}
```

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| confirm | 确认选择时触发 | result: { ident, value, paths } |
| close | 关闭弹窗时触发 | { ident } |

### 回调结果格式

```javascript
{
  ident: 'cascader-city',      // 弹窗标识
  value: [                     // 选中的项目数组
    {
      id: '110105',            // 选中项的值
      name: '朝阳区',          // 选中项的标签
      path: ['110000', '110100', '110105'],        // 完整路径值
      pathLabels: ['北京市', '北京市', '朝阳区'],   // 完整路径标签
      level: 2                 // 选中项的层级
    }
  ],
  paths: [                     // 路径数组（根据 emitPath 配置）
    ['110000', '110100', '110105']  // 完整路径或仅末级值
  ]
}
```

## 使用场景

### 1. 省市区选择（3级）

```vue
<el-cascader-city-popup
  :level="3"
  :multiple="false"
  title="选择省市区"
  @confirm="onConfirm"
/>
```

### 2. 省市选择（2级）

```vue
<el-cascader-city-popup
  :level="2"
  :multiple="false"
  title="选择省市"
  @confirm="onConfirm"
/>
```

### 3. 多选城市

```vue
<el-cascader-city-popup
  :level="3"
  :multiple="true"
  :max="5"
  title="选择多个城市"
  @confirm="onConfirm"
/>
```

### 4. 网格显示模式

```vue
<el-cascader-city-popup
  displayMode="grid"
  title="选择城市"
  @confirm="onConfirm"
/>
```

### 5. 自定义API和缓存

```vue
<el-cascader-city-popup
  apiUrl="/api/custom/city"
  cacheKey="custom-city-cache"
  :forceRefresh="true"
  @confirm="onConfirm"
/>
```

## 数据格式

组件期望的城市数据格式：

```javascript
[
  {
    id: '110000',           // 城市编码
    name: '北京市',         // 城市名称
    children: [             // 子级城市
      {
        id: '110100',
        name: '北京市',
        children: [
          { id: '110101', name: '东城区' },
          { id: '110102', name: '西城区' }
        ]
      }
    ]
  }
]
```

## 缓存机制

### 自动缓存
- 首次请求成功后自动缓存数据到本地存储
- 后续使用优先读取缓存，提升加载速度

### 缓存控制
```javascript
// 强制刷新数据
<el-cascader-city-popup :forceRefresh="true" />

// 自定义缓存键
<el-cascader-city-popup cacheKey="my-city-cache" />

// 清除缓存
uni.removeStorageSync('a-citys-link-list');
```

## 与原组件的对比

| 功能 | popup-multiple-city | popup-cascader | **popup-cascader-city** |
|------|---------------------|----------------|-------------------------|
| 数据来源 | 接口获取 | Props传入 | ✅ **接口获取** |
| 缓存机制 | ✅ 支持 | ❌ | ✅ **支持** |
| 单选/多选 | 多选 | 单选/多选 | ✅ **单选/多选** |
| 级联层级 | 1-2级 | 任意级 | ✅ **1-5级** |
| 显示模式 | 列表/网格 | 列表/网格 | ✅ **列表/网格** |
| 末级回显 | ❌ | ✅ 支持 | ✅ **支持** |
| Element风格 | ❌ | ✅ 支持 | ✅ **支持** |

## 注意事项

1. **API接口**：确保API返回的数据格式符合组件要求
2. **网络处理**：组件会自动处理网络错误并显示提示
3. **缓存管理**：合理使用缓存键避免数据冲突
4. **性能优化**：大数据量时建议限制层级数量
5. **兼容性**：支持 UniApp 各平台

## 更新日志

### v1.0.0
- 🎉 初始版本发布
- ✨ 集成城市数据请求功能
- ✨ 智能缓存机制
- ✨ Element Plus 设计风格
- ✨ 支持1-5级城市选择
- ✨ 完整的错误处理机制

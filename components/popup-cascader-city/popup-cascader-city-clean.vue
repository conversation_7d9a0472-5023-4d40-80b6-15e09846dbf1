<template>
	<view class="el-cascader-popup">
		<view class="el-cascader">
			<!-- 标题栏 -->
			<view class="el-cascader__header">
				<text class="el-cascader__title">{{ title }}</text>
				<view class="el-cascader__close" @click="handleClose">
					<image src="/static/image/ic_close.png" mode="aspectFit"></image>
				</view>
			</view>

			<!-- 加载状态 -->
			<view v-if="loading" class="el-cascader__loading">
				<u-loading-icon mode="spinner" size="40"></u-loading-icon>
				<text class="el-cascader__loading-text">正在加载城市数据...</text>
			</view>

			<!-- 级联选择面板 -->
			<view v-else class="el-cascader__panel" :style="{ height: panelHeight + 'rpx' }">
				<!-- 一级选项 -->
				<scroll-view 
					scroll-y 
					class="el-cascader__menu el-cascader__menu--first" 
					:scroll-into-view="scrollIntoView[0]"
				>
					<view 
						:id="`level-0-${item[valueKey]}`"
						class="el-cascader__node" 
						:class="{ 
							'is-active': isSelected(item),
							'is-disabled': item[disabledKey] 
						}"
						v-for="(item, index) in cityOptions" 
						:key="item[valueKey]"
						@click="handleNodeClick(item, 0, index)"
					>
						<text class="el-cascader__label">
							{{ item[labelKey] }}
							<text v-if="multiple && getChildrenCount(item, 0) > 0" class="el-cascader__count">
								({{ getChildrenCount(item, 0) }})
							</text>
						</text>
						<image 
							v-if="!multiple || isLeaf(0)" 
							class="el-cascader__icon" 
							:src="isSelected(item) ? checkedIcon : uncheckedIcon" 
							mode="aspectFit"
						></image>
					</view>
				</scroll-view>

				<!-- 二级及以上选项 -->
				<scroll-view 
					v-for="(menuData, menuIndex) in activeMenus" 
					:key="menuIndex"
					scroll-y 
					class="el-cascader__menu" 
					:class="{ 'el-cascader__menu--grid': displayMode === 'grid' && menuIndex === activeMenus.length - 1 }"
					:scroll-into-view="scrollIntoView[menuIndex + 1]"
				>
					<view v-if="displayMode === 'list' || menuIndex < activeMenus.length - 1" class="el-cascader__nodes">
						<view 
							:id="`level-${menuIndex + 1}-${item[valueKey]}`"
							class="el-cascader__node" 
							:class="{ 
								'is-active': isSelected(item),
								'is-disabled': item[disabledKey] 
							}"
							v-for="(item, index) in menuData" 
							:key="item[valueKey]"
							@click="handleNodeClick(item, menuIndex + 1, index)"
						>
							<text class="el-cascader__label">{{ item[labelKey] }}</text>
							<image 
								v-if="!multiple || isLeaf(menuIndex + 1)" 
								class="el-cascader__icon" 
								:src="isSelected(item) ? checkedIcon : uncheckedIcon" 
								mode="aspectFit"
							></image>
						</view>
					</view>
					
					<!-- 网格模式 -->
					<view v-else class="el-cascader__grid">
						<view 
							:id="`level-${menuIndex + 1}-${item[valueKey]}`"
							class="el-cascader__grid-item" 
							:class="{ 
								'is-active': isSelected(item),
								'is-disabled': item[disabledKey] 
							}"
							v-for="(item, index) in menuData" 
							:key="item[valueKey]"
							@click="handleNodeClick(item, menuIndex + 1, index)"
						>
							<text class="el-cascader__grid-label">{{ item[labelKey] }}</text>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 已选择项展示和确认按钮 -->
			<view v-if="shouldShowFooter" class="el-cascader__footer">
				<view class="el-cascader__tags">
					<view 
						class="el-cascader__tag" 
						v-for="item in selectedNodes" 
						:key="getNodeKey(item)"
					>
						{{ getNodeLabel(item) }}
						<image 
							src="/static/image/ic_tag_delete.png" 
							@click="handleRemoveTag(item)" 
							mode="aspectFit"
						></image>
					</view>
				</view>
				<view class="el-cascader__confirm" @click="handleConfirm">
					{{ confirmText }}
				</view>
			</view>
		</view>
		<u-safe-bottom :customStyle="{ background: '#FFF' }"></u-safe-bottom>
	</view>
</template>

<script>
/**
 * Element Plus 风格城市级联选择器组件
 * @description 集成城市数据请求功能的级联选择器，自动从接口获取城市数据并缓存
 * @version 1.0.0
 */
export default {
	name: 'ElCascaderCityPopup',
	props: {
		// 弹窗标识
		ident: {
			type: String,
			default: 'cascader-city'
		},
		// 弹窗显示状态
		show: {
			type: Boolean,
			default: false
		},
		// 弹窗标题
		title: {
			type: String,
			default: '选择城市'
		},
		// 级联层级数量
		level: {
			type: Number,
			default: 3,
			validator: (value) => value > 0 && value <= 5
		},
		// 是否支持多选
		multiple: {
			type: Boolean,
			default: false
		},
		// 最大选择数量（多选时有效）
		max: {
			type: Number,
			default: 1,
			validator: (value) => value > 0
		},
		// 显示模式：list（列表） | grid（网格）
		displayMode: {
			type: String,
			default: 'list',
			validator: (value) => ['list', 'grid'].includes(value)
		},
		// 数据源字段配置
		props: {
			type: Object,
			default: () => ({
				value: 'id',
				label: 'name', 
				children: 'children',
				disabled: 'disabled'
			})
		},
		// 选中值列表（支持末级编号传入）
		modelValue: {
			type: Array,
			default: () => []
		},
		// 是否严格模式（只能选择叶子节点）
		checkStrictly: {
			type: Boolean,
			default: true
		},
		// 是否返回完整路径
		emitPath: {
			type: Boolean,
			default: true
		},
		// 面板高度（单位：rpx）
		panelHeight: {
			type: Number,
			default: 800,
			validator: (value) => value > 0
		},
		// 完成选择回调函数
		complete: {
			type: Function,
			default: () => {}
		},
		// 城市数据API地址
		apiUrl: {
			type: String,
			default: '/system/area/tree'
		},
		// 缓存键名
		cacheKey: {
			type: String,
			default: 'a-citys-link-list'
		},
		// 是否强制刷新数据
		forceRefresh: {
			type: Boolean,
			default: false
		}
	},
	
	data() {
		return {
			// 城市数据选项
			cityOptions: [],
			// 当前激活的各级菜单数据
			activeMenus: [],
			// 当前选中的节点列表
			selectedNodes: [],
			// 滚动视图定位ID列表
			scrollIntoView: [],
			// 当前选中的路径索引列表
			activeIndexes: [],
			// 加载状态
			loading: false
		};
	},
	
	computed: {
		// 数据字段映射 - 值字段键名
		valueKey() {
			return this.props.value || 'id';
		},
		
		// 数据字段映射 - 标签字段键名
		labelKey() {
			return this.props.label || 'name';
		},
		
		// 数据字段映射 - 子项字段键名
		childrenKey() {
			return this.props.children || 'children';
		},
		
		// 数据字段映射 - 禁用字段键名
		disabledKey() {
			return this.props.disabled || 'disabled';
		},
		
		// 选中状态图标路径
		checkedIcon() {
			return this.multiple ? '/static/image/ic_checkbox_sel.png' : '/static/image/ic_check_sel.png';
		},
		
		// 未选中状态图标路径
		uncheckedIcon() {
			return this.multiple ? '/static/image/ic_checkbox_nor.png' : '/static/image/ic_check_nor.png';
		},
		
		// 确认按钮文本
		confirmText() {
			if (this.multiple && this.max > 1) {
				return `确认选择（${this.selectedNodes.length}/${this.max}）`;
			}
			return '确认选择';
		},
		
		// 是否显示底部区域
		shouldShowFooter() {
			// 多选模式：当max>1时显示，或者有选中项时显示
			if (this.multiple) {
				return this.max > 1 || this.selectedNodes.length > 0;
			}
			// 单选模式：当max>1时显示（需要手动确认），max=1时不显示（自动确认）
			return this.max > 1 && this.selectedNodes.length > 0;
		}
	},
	
	watch: {
		// 监听弹窗显示状态变化
		show(newVal) {
			if (newVal) {
				this.init();
			}
		},
		
		// 监听选中值变化
		modelValue: {
			handler() {
				if (this.cityOptions.length > 0) {
					this.initSelectedNodes();
				}
			},
			deep: true
		}
	},
	
	created() {
		this.init();
	},

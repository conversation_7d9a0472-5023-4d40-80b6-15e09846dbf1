<template>
	<view class="example-page">
		<view class="demo-section">
			<text class="section-title">城市级联选择器示例</text>
			
			<!-- 单选示例 -->
			<view class="demo-item">
				<text class="demo-title">单选模式</text>
				<view class="demo-btn" @click="showSingleSelect">
					{{ singleResult.length > 0 ? singleResult[0].pathLabels.join(' / ') : '请选择城市' }}
				</view>
			</view>
			
			<!-- 多选示例 -->
			<view class="demo-item">
				<text class="demo-title">多选模式（最多3个）</text>
				<view class="demo-btn" @click="showMultipleSelect">
					{{ multipleResult.length > 0 ? `已选择${multipleResult.length}项` : '请选择城市' }}
				</view>
				<view v-if="multipleResult.length > 0" class="selected-list">
					<text v-for="item in multipleResult" :key="item.id" class="selected-item">
						{{ item.pathLabels.join(' / ') }}
					</text>
				</view>
			</view>
			
			<!-- 网格模式示例 -->
			<view class="demo-item">
				<text class="demo-title">网格模式</text>
				<view class="demo-btn" @click="showGridSelect">
					{{ gridResult.length > 0 ? gridResult[0].pathLabels.join(' / ') : '请选择城市' }}
				</view>
			</view>
			
			<!-- 省市选择示例 -->
			<view class="demo-item">
				<text class="demo-title">省市选择（2级）</text>
				<view class="demo-btn" @click="showProvinceCity">
					{{ provinceCityResult.length > 0 ? provinceCityResult[0].pathLabels.join(' / ') : '请选择省市' }}
				</view>
			</view>
			
			<!-- 操作按钮 -->
			<view class="demo-item">
				<text class="demo-title">数据操作</text>
				<view class="action-buttons">
					<view class="action-btn" @click="refreshData">刷新数据</view>
					<view class="action-btn" @click="clearCache">清除缓存</view>
				</view>
			</view>
		</view>
		
		<!-- 城市级联选择器弹窗 -->
		<u-popup v-model="popupShow" mode="bottom" border-radius="20" :safe-area-inset-bottom="false">
			<el-cascader-city-popup
				:show="popupShow"
				:title="currentConfig.title"
				:level="currentConfig.level"
				:multiple="currentConfig.multiple"
				:max="currentConfig.max"
				:displayMode="currentConfig.displayMode"
				:modelValue="currentConfig.modelValue"
				:apiUrl="currentConfig.apiUrl"
				:cacheKey="currentConfig.cacheKey"
				:forceRefresh="currentConfig.forceRefresh"
				@confirm="onCascaderConfirm"
				@close="onCascaderClose"
			/>
		</u-popup>
	</view>
</template>

<script>
import ElCascaderCityPopup from './popup-cascader-city.vue';

export default {
	name: 'CascaderCityExample',
	components: {
		ElCascaderCityPopup
	},
	data() {
		return {
			popupShow: false,
			currentConfig: {},
			
			// 单选结果
			singleResult: [],
			// 多选结果
			multipleResult: [],
			// 网格模式结果
			gridResult: [],
			// 省市选择结果
			provinceCityResult: []
		};
	},
	methods: {
		// 显示单选
		showSingleSelect() {
			this.currentConfig = {
				title: '选择城市（单选）',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				modelValue: this.singleResult.map(item => item.id),
				apiUrl: '/system/area/tree',
				cacheKey: 'city-single-cache',
				forceRefresh: false
			};
			this.popupShow = true;
		},
		
		// 显示多选
		showMultipleSelect() {
			this.currentConfig = {
				title: '选择城市（多选）',
				level: 3,
				multiple: true,
				max: 3,
				displayMode: 'list',
				modelValue: this.multipleResult.map(item => item.id),
				apiUrl: '/system/area/tree',
				cacheKey: 'city-multiple-cache',
				forceRefresh: false
			};
			this.popupShow = true;
		},
		
		// 显示网格模式
		showGridSelect() {
			this.currentConfig = {
				title: '选择城市（网格）',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'grid',
				modelValue: this.gridResult.map(item => item.id),
				apiUrl: '/system/area/tree',
				cacheKey: 'city-grid-cache',
				forceRefresh: false
			};
			this.popupShow = true;
		},
		
		// 显示省市选择
		showProvinceCity() {
			this.currentConfig = {
				title: '选择省市（2级）',
				level: 2,
				multiple: false,
				max: 1,
				displayMode: 'list',
				modelValue: this.provinceCityResult.map(item => item.id),
				apiUrl: '/system/area/tree',
				cacheKey: 'province-city-cache',
				forceRefresh: false
			};
			this.popupShow = true;
		},
		
		// 刷新数据
		refreshData() {
			this.currentConfig = {
				title: '刷新城市数据',
				level: 3,
				multiple: false,
				max: 1,
				displayMode: 'list',
				modelValue: [],
				apiUrl: '/system/area/tree',
				cacheKey: 'city-refresh-cache',
				forceRefresh: true // 强制刷新
			};
			this.popupShow = true;
		},
		
		// 清除缓存
		clearCache() {
			// 清除所有相关缓存
			const cacheKeys = [
				'city-single-cache',
				'city-multiple-cache', 
				'city-grid-cache',
				'province-city-cache',
				'city-refresh-cache',
				'a-citys-link-list' // 默认缓存键
			];
			
			cacheKeys.forEach(key => {
				uni.removeStorageSync(key);
			});
			
			uni.showToast({
				title: '缓存已清除',
				icon: 'success'
			});
		},
		
		// 级联选择确认
		onCascaderConfirm(result) {
			console.log('选择结果：', result);
			
			// 根据当前配置更新对应的结果
			if (this.currentConfig.title.includes('单选')) {
				this.singleResult = result.value;
			} else if (this.currentConfig.title.includes('多选')) {
				this.multipleResult = result.value;
			} else if (this.currentConfig.title.includes('网格')) {
				this.gridResult = result.value;
			} else if (this.currentConfig.title.includes('省市')) {
				this.provinceCityResult = result.value;
			}
		},
		
		// 级联选择关闭
		onCascaderClose() {
			this.popupShow = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.example-page {
	padding: 30rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.demo-section {
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 40rpx;
}

.demo-item {
	margin-bottom: 40rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.demo-title {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 20rpx;
}

.demo-btn {
	height: 80rpx;
	padding: 0 30rpx;
	background: #f8f8f8;
	border: 1px solid #e0e0e0;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	font-size: 30rpx;
	color: #333333;
}

.selected-list {
	margin-top: 20rpx;
}

.selected-item {
	display: block;
	padding: 10rpx 20rpx;
	margin-bottom: 10rpx;
	background: #ecf5ff;
	border: 1px solid #409eff;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #409eff;
}

.action-buttons {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	background: #409eff;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #ffffff;
	font-weight: 500;
	
	&:active {
		background: #3a8ee6;
	}
}
</style>

<template>
	<view>
		<z-paging>
			<template #top>
				<u-navbar title="名片信息" :statusBar="true" :placeholder="true" :autoBack="true" />
			</template>

			<view class="flex audit-wrap" v-if="!$u.test.isEmpty(audtiInfo)">
				<image :src="iImage('ic_shuom.png', 'pagesMine')" mode="aspectFit"></image>
				<text>{{ audtiInfo.text }}</text>
			</view>

			<view class="flex section-wrap">
				<view class="flex section-wrap__pic-cell">
					<view class="flex-col section-wrap__pic-cell_title" :class="{ verify: formVerify && $u.test.isEmpty(form.avatarOrLogoUrl) }">
						<text class="section-wrap__pic-cell__text1">{{ providerType == cons.PROVIDER_TYPE_PERSONAL ? '上传头像' : '上传 Logo/头像' }}</text>
						<text class="section-wrap__pic-cell__text2">提交让客户眼前一亮的图片，打造 专属的个性化品牌</text>
					</view>
					<view class="section-wrap__pic-cell__content" @click="chooseAvatarImage">
						<view class="flex-col section-wrap__pic-cell__content__add" v-if="$u.test.isEmpty(form.avatarOrLogoUrl)">
							<image :src="iImage('ic_pic_add.png')" mode="aspectFit"></image>
							<text>点击上传</text>
						</view>
						<image class="section-wrap__pic-cell__content__img" :src="form.avatarOrLogoUrl" mode="aspectFill" v-else />
					</view>
				</view>
			</view>

			<!-- 基本信息 -->
			<view class="flex-col section-wrap">
				<text class="section-wrap__title">基本信息</text>
				<view class="flex section-wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.contactWechat) }">
					<text>对外微信</text>
					<input v-model="form.contactWechat" placeholder="请填写微信号" />
				</view>
				<view class="flex section-wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.contactMobile) }">
					<text>对外电话</text>
					<input v-model="form.contactMobile" placeholder="请填写电话号码" />
				</view>
				<view class="flex section-wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.experienceDesc) }">
					<text>服务经验</text>
					<input v-model="form.experienceDesc" placeholder="请填写 如:从业n年或服务n+家" />
				</view>
				<view class="flex section-wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.addressValue) }" @click="onShowSelectPopup('city')">
					<text>所在城市</text>
					<input :value="addressValue" placeholder="请选择所在城市" disabled />
				</view>
				<view class="flex section-wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.addressDetail) }" v-if="providerType == cons.PROVIDER_TYPE_COMPANY">
					<text>公司地址</text>
					<input v-model="form.addressDetail" placeholder="请填写公司地址" />
				</view>
			</view>

			<!-- 服务信息 -->
			<view class="flex-col section-wrap">
				<text class="section-wrap__title">服务信息</text>
				<view class="flex section-wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.serviceCategoryValue) }" @click="onShowSelectPopup('service')">
					<text>主营业务</text>
					<input :value="serviceCategoryValue" placeholder="请选择主营业务(多选)" disabled />
					<image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
				</view>
				<view class="flex section-wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.serviceRegionValue) }" @click="onShowSelectPopup('serviceRegion')">
					<text>服务地区</text>
					<input :value="serviceRegionValue" placeholder="请选择服务地区" disabled />
					<image class="section-wrap__normal-cell__arrow" :src="iImage('ic_small_arrow_right.png')" mode="aspectFit" />
				</view>
				<view class="flex-col section-wrap__normal-cell" :class="{ verify: formVerify && $u.test.isEmpty(form.description) }">
					<text>服务内容</text>
					<view>
						<textarea class="section-wrap__normal-cell__textarea" v-model="form.description" placeholder="请简单介绍一下所服务的内容（200字以内）" :maxlength="200" />
					</view>
				</view>
			</view>
			<!-- 营销宣传 -->
			<view class="flex-col section-wrap" :class="{ verify: formVerify && $u.test.isEmpty(form.promotionPicUrls) }">
				<text class="section-wrap__title">营销宣传</text>
				<view class="flex section-wrap__pics-cell">
					<!-- 添加按钮（当未达到最大数量时显示） -->
					<view v-if="form.promotionPicUrls.length < maxPicCount" class="item section-wrap__pics-cell__add" @click="choosePromotionImage">
						<image :src="iImage('ic_pic_add.png')" mode="aspectFit"></image>
						<text>添加图片</text>
					</view>
					<!-- 已上传图片列表 -->
					<view v-for="(url, idx) in form.promotionPicUrls" :key="idx" class="item section-wrap__pics-cell__img">
						<image class="section-wrap__pics-cell__img__preview" :src="url" mode="aspectFill" @click="previewImage(index)" />
						<image class="section-wrap__pics-cell__img__delete" :src="iImage('ic_item_delete.png', 'pagesMine')" @click.stop="deleteImage(index)" />
					</view>
				</view>
			</view>

			<template #bottom>
				<view class="flex submit" @click="onPrepareSubmit">提交审核</view>
			</template>
		</z-paging>
		<!-- 城市选择器 -->
		<u-popup :show="cityPopup.show" bgColor="transparent" mode="bottom" :safe-area-inset-bottom="false" closeable @close="cityPopup.show = false">
			<popup-city :length="cityPopup.level" :show="cityPopup.show" :value="cityPopup.value" :complete="cityPopup.complete" @close="cityPopup.show = false"></popup-city>
		</u-popup>
		<!-- 多选城市选择器 -->
		<u-popup :show="multipleCityPopup.show" bgColor="transparent" mode="bottom" :safe-area-inset-bottom="false" closeable @close="multipleCityPopup.show = false">
			<popup-multiple-city :max-num="6" :show="multipleCityPopup.show" :value="multipleCityPopup.value" :complete="multipleCityPopup.complete" @close="multipleCityPopup.show = false"></popup-multiple-city>
		</u-popup>
		<!-- 选择器 -->
		<u-popup :show="selectPopup.show" bgColor="transparent" mode="bottom" :close-on-click-overlay="false" :safe-area-inset-bottom="false" closeable @close="selectPopup.show = false">
			<popup-select
				:title="selectPopup.title"
				:list="selectPopup.list"
				:value="selectPopup.value"
				:max-num="selectPopup.maxNum"
				:show-input="selectPopup.showInput"
				:complete="selectPopup.complete"
				@close="selectPopup.show = false"
			></popup-select>
		</u-popup>
		<!-- 级联选择器 -->
		<u-popup :show="linkSelectPopup.show" bgColor="transparent" mode="bottom" :safe-area-inset-bottom="false" closeable @close="linkSelectPopup.show = false">
			<popup-link-select
				:title="linkSelectPopup.title"
				:level="linkSelectPopup.level"
				:list="linkSelectPopup.list"
				:value="linkSelectPopup.value"
				:max-num="linkSelectPopup.maxNum"
				:complete="linkSelectPopup.complete"
				@close="linkSelectPopup.show = false"
			></popup-link-select>
		</u-popup>
		<!-- 通用操作弹窗 -->
		<u-popup :show="promptPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" @close="promptPopup.show = false">
			<popup-prompt
				:ident="promptPopup.ident"
				:icon="promptPopup.icon"
				:iconStyle="promptPopup.iconStyle"
				:title="promptPopup.title"
				:message="promptPopup.message"
				:actions="promptPopup.actions"
				:tipText="promptPopup.tipText"
				:buttonBoxJustifyContent="promptPopup.buttonBoxJustifyContent"
				:customStyle="promptPopup.customStyle"
				:showClose="promptPopup.showClose"
				@cancel="promptPopup.show = false"
			></popup-prompt>
		</u-popup>
	</view>
</template>

<script>
import { selectImage } from '@/common/utils.js';
import { handleTree } from '@/common/tool.js';
import { h } from 'vue';
export default {
	name: 'business-form',
	data() {
		return {
			pageParams: {},

			// 最大图片数量
			maxPicCount: 6,
			// 表单数据
			form: {
				// 头像或者logo
				avatarOrLogoUrl: '',
				// 对外微信号
				contactMobile: '',
				// 对外手机号
				contactWechat: '',
				// 经验描述
				experienceDesc: '',
				// 服务类目
				serviceCategory: [],
				// 服务区域
				serviceRegions: [],
				// 所在地址
				address: {},
				addressDetail: '',
				// 服务描述
				description: '',
				// 营销图片
				promotionPicUrls: []
			},
			// 服务商资料
			profileDetial: {},
			// 表单验证
			formVerify: false,

			// 城市选择器
			cityPopup: {
				show: false
			},
			// 城市选择器
			multipleCityPopup: {
				show: false
			},
			// 选择器
			selectPopup: {
				show: false,
				list: []
			},
			// 选择器
			linkSelectPopup: {
				show: false,
				list: []
			},
			// 通用弹窗
			promptPopup: {
				show: false
			}
		};
	},
	computed: {
		// 审核信息
		audtiInfo() {
			if (this.profileDetial?.auditStatus == this.cons.PROVIDER_PROFILE_AUDIT_STATUS_PENDING) {
				return { text: '审核中' };
			} else if (this.profileDetial?.auditStatus == this.cons.PROVIDER_PROFILE_AUDIT_STATUS_REJECTED) {
				return { text: this.profileDetial?.auditReason };
			}
			return null;
		},
		// 服务商类型
		// 优先使用传入的认证类型作为服务商类型[从实名认证页面进入会传递选择的认证类型]
		providerType() {
			if (uni.$u.test.isEmpty(this.pageParams?.certType)) {
				return this.pageParams?.certType;
			}
			if (!uni.$u.test.isEmpty(this.profileDetial?.providerType)) {
				return this.profileDetial?.providerType;
			}
			// 默认个人服务商
			return this.cons.PROVIDER_TYPE_PERSONAL;
		},
		// 所在区域
		addressValue() {
			return [this.form.address.provinceName, this.form.address.cityName, this.form.address.districtName].filter((e) => !uni.$u.test.isEmpty(e)).join('/');
		},
		// 服务器区域名称
		serviceRegionValue() {
			const list = this.form.serviceRegions.flatMap((e) => e.child);
			return list.map((e) => e.name).join('、');
		},
		// 服务类型名称
		serviceCategoryValue() {
			if (!uni.$u.test.isEmpty(this.form.serviceCategory)) {
				return this.form.serviceCategory.map((e) => e.secondName).join('、');
			}
			return '';
		}
	},
	onLoad(options) {
		this.pageParams = options;

		this.queryProfile();
	},
	methods: {
		// 展示选择器
		onShowSelectPopup(type) {
			switch (type) {
				case 'serviceRegion':
					this.multipleCityPopup = {
						show: true,
						value: this.form.serviceRegions,
						complete: (res) => {
							this.form.serviceRegion = res.detail;
						}
					};
					break;
				case 'city':
					this.cityPopup = {
						show: true,
						level: 3,
						value: this.form.address,
						complete: (res) => {
							this.form.address = res;
						}
					};
					break;
				case 'service':
					uni.$u.http
						.get('/category/common/list-by-type', {
							params: {
								type: this.cons.CATEGORY_TYPE_SERVICE
							}
						})
						.then((res) => {
							this.linkSelectPopup = {
								show: true,
								level: 2,
								maxNum: 3,
								list: handleTree(res),
								value: this.form.serviceCategory,
								complete: (res) => {
									console.error('====', res);
									this.form.serviceCategory = res;
								}
							};
						});
					break;
			}
		},
		// 选择头像LOGO图片
		chooseAvatarImage() {
			selectImage((result) => {
				if (result.filePaths.length === 0) return;
				uni.$u.http
					.upload('/infra/file/upload', {
						params: {
							directory: 'profile'
						},
						filePath: result.filePaths[0],
						name: 'file'
					})
					.then((data) => {
						this.form.avatarOrLogoUrl = data;
					});
			}, 1);
		},
		// 选择营销图片
		choosePromotionImage() {
			selectImage((result) => {
				if (result.filePaths.length === 0) return;
				uni.showLoading({
					title: `上传中(0/${result.filePaths.length})`,
					icon: 'none',
					mask: true
				});

				// 存储所有上传任务
				const uploadTasks = result.filePaths.map((filePath, index) => {
					return uni.$u.http
						.upload('/infra/file/upload', {
							params: {
								directory: 'profile'
							},
							filePath: filePath,
							name: 'file'
						})
						.then((data) => {
							// 更新上传进度
							uni.showLoading({
								title: `上传中(${index + 1}/${result.filePaths.length})`,
								icon: 'none',
								mask: true
							});
							return data;
						});
				});

				// 批量执行上传
				Promise.all(uploadTasks)
					.then((allData) => {
						uni.hideLoading();
						// 全部上传成功后添加到列表
						this.form.promotionPicUrls.push(...allData);
						uni.showToast({
							title: `成功上传${allData.length}张图片`,
							icon: 'none'
						});
					})
					.catch((err) => {
						uni.hideLoading();
						uni.showToast({
							title: `上传失败: ${err.message || '未知错误'}`,
							icon: 'none'
						});
					});
			}, this.maxPicCount - this.form.promotionPicUrls.length);
		},

		// 删除图片
		deleteImage(index) {
			this.form.promotionPicUrls.splice(index, 1);
		},

		// 预览图片
		previewImage(index) {
			uni.previewImage({
				current: index,
				urls: this.form.promotionPicUrls
			});
		},
		onPrepareSubmit() {
			this.formVerify = true;

			const validations = [
				{ condition: uni.$u.test.isEmpty(this.form.avatarOrLogoUrl), message: '请上传头像/品牌图片' },
				{ condition: uni.$u.test.isEmpty(this.form.contactWechat), message: '请填写对外微信' },
				{ condition: uni.$u.test.isEmpty(this.form.contactMobile), message: '请填写对外电话' },
				{ condition: uni.$u.test.isEmpty(this.form.experienceDesc), message: '请填写经验描述' },
				{ condition: uni.$u.test.isEmpty(this.form.address), message: '请选择所在城市' },
				{ condition: uni.$u.test.isEmpty(this.form.addressDetail) && this.providerType == this.cons.PROVIDER_TYPE_PERSONAL, message: '请填写公司地址' },
				{ condition: uni.$u.test.isEmpty(this.form.serviceCategory), message: '请选择主营业务' },
				{ condition: uni.$u.test.isEmpty(this.form.serviceRegions), message: '请选择服务地区' },
				{ condition: uni.$u.test.isEmpty(this.form.description), message: '请填写服务内容' },
				{ condition: uni.$u.test.isEmpty(this.form.promotionPicUrls), message: '请选择营销宣传图片' }
			];
			for (const e of validations) {
				if (e.condition) {
					uni.showToast({
						title: e.message,
						icon: 'none'
					});
					return;
				}
			}

			this.onSubmit();
		},
		// 提交
		onSubmit() {
			uni.$u.http
				.post(
					'/member/provider-profile/submit',
					{
						providerType: this.providerType,
						avatarUrl: this.form.avatarOrLogoUrl,
						brandLogoUrl: this.form.avatarOrLogoUrl,
						contactWechat: this.form.contactWechat,
						contactMobile: this.form.contactMobile,
						experienceDesc: this.form.experienceDesc,
						description: this.form.description,
						serviceRegionCodes: [this.form.serviceRegion.cityId],
						provinceCode: this.form.address.provinceId,
						cityCode: this.form.address.cityId,
						districtCode: this.form.address.districtId,
						addressDetail: this.form.addressDetail,
						serviceCategoryIds: this.form.serviceCategory.map((e) => e.secondId),
						promotionPicUrls: this.form.promotionPicUrls
					},
					{
						custom: {
							showLoading: true,
							loadingMsg: '提交中..'
						}
					}
				)
				.then((res) => {
					this.promptPopup = {
						show: true,
						ident: 'prompt',
						icon: this.iImage('ic_popup_success.png'),
						iconStyle: 'widht:100rpx;height:100rpx;',
						title: '提交成功',
						message: '您的资料已提交成功<br/>请等待审核 ',
						actions: [
							{
								type: 'ok',
								title: '知道了',
								customStyle: 'width:240rpx;height:90rpx;border-radius: 45rpx;background: #444BF1;color:#FFFFFF;',
								action: () => {
									console.error('----', res);
									this.onBack();
								}
							}
						],
						showClose: false
					};
				})
				.catch((err) => {
					uni.hideLoading();
					// TODO 根据状态码提示发布上线
					// this.promptPopup = {
					// 	show: true,
					// 	ident: 'prompt',
					// 	title: '发布上限',
					// 	message: '您的发布已达上限<br/>请编辑或者删除服务重新发布 ',
					// 	actions: [
					// 		{
					// 			type: 'ok',
					// 			title: '知道了',
					// 			customStyle: 'width:480rpx;height:90rpx;border-radius: 45rpx;background: #444BF1;color:#FFFFFF;',
					// 			action: () => {
					// 				this.onBack();
					// 			}
					// 		}
					// 	],
					// 	showClose: false
					// };
				});
		},
		queryProfile() {
			uni.$u.http
				.get('/provider/user/get-editable-profile')
				.then((res) => {
					this.profileDetial = res || {};
					// 回显表单数据
					this.form.avatarOrLogoUrl = res.providerType == this.cons.PROVIDER_TYPE_PERSONAL ? res.avatarUrl : res.brandLogoUrl;
					this.form.contactWechat = res.contactWechat || '';
					this.form.contactMobile = res.contactMobile || '';
					this.form.experienceDesc = res.experienceDesc || '';
					this.form.description = res.description || '';
					// 主营业务
					const categoryTree = handleTree(res.serviceCategoryList);
					this.form.serviceCategory = categoryTree.flatMap((first) =>
						(first.child || []).map((second) => ({
							secondId: second.id,
							secondName: second.name
						}))
					);

					// 所在区域
					this.form.address = { provinceCode: res.provinceCode, provinceName: res.provinceName, cityCode: res.cityCode, cityName: res.cityName, districtCode: res.districtCode, districtName: res.districtName };
					this.form.addressDetail = res.addressDetail || '';
					// 服务区域
					this.form.serviceRegions = handleTree(res.serviceRegionList, 'id', 'parentId', 'child', 1);

					this.form.promotionPicUrls = res.promotionPicUrls || [];

					console.error('====', this.form);
				})
				.catch((err) => {});
		}
	}
};
</script>

<style lang="scss" scoped>
.audit-wrap {
	width: 100%;
	padding: 20rpx 30rpx;
	background-color: #ffeff0;
	border-radius: 15rpx;
	font-weight: 400;
	font-size: 26rpx;
	color: #f43a55;
	line-height: 36rpx;
	image {
		width: 36rpx;
		height: 36rpx;
		margin-right: 10rpx;
		flex-shrink: 0;
	}
}

.section-wrap {
	padding: 0 30rpx;
	margin-top: 10rpx;
	background-color: #ffffff;

	&:first-child {
		margin-top: 0;
	}

	&__title {
		margin-top: 30rpx;
		font-weight: bold;
		font-size: 36rpx;
		color: #1e2134;
	}

	&__pic-cell {
		flex: 1;
		padding: 30rpx 0;
		justify-content: space-between;
		align-items: flex-start;

		&__text1 {
			font-weight: 500;
			font-size: 30rpx;
			color: #1e2134;
			white-space: nowrap;

			&:before {
				content: '*';
				color: red;
			}
		}

		&__text2 {
			max-width: 360rpx;
			margin-top: 15rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #909099;
			line-height: 30rpx;
		}

		&__content {
			width: 120rpx;
			height: 120rpx;
			border-radius: 10rpx;
			overflow: hidden;

			&__add {
				width: 100%;
				height: 100%;
				border-radius: 10rpx;
				border: 1px solid #d8d8db;
				align-items: center;
				justify-content: center;

				image {
					width: 40rpx;
					height: 36rpx;
				}

				text {
					margin-top: 10rpx;
					font-size: 24rpx;
					color: #909099;
				}
			}

			&__img {
				width: 100%;
				height: 100%;
				background-color: #d8d8db;
			}
		}
	}

	&__normal-cell {
		padding: 30rpx 0;

		text {
			font-weight: 500;
			font-size: 30rpx;
			color: #1e2134;
			white-space: nowrap;

			&:before {
				content: '*';
				color: red;
			}
		}

		input {
			flex: 1;
			margin-left: 45rpx;
			font-size: 30rpx;
		}

		&__textarea {
			width: 100%;
			height: 200rpx;
			padding: 20rpx;
			margin-top: 30rpx;
			border-radius: 10rpx;
			border: 0.8px solid #d8d8db;
		}

		&__arrow {
			width: 10rpx;
			height: 17rpx;
		}
	}

	&__pics-cell {
		display: flex;
		flex-wrap: wrap;
		margin: 30rpx -7rpx 60rpx -7rpx;
		/* 抵消子项margin实现间距 */

		.item {
			width: 220rpx;
			height: 166rpx;
			margin: 7rpx;
			/* 14rpx的一半，因为两边都有 */
			position: relative;
			border-radius: 8rpx;
			overflow: visible;
		}

		/* 添加按钮样式 */
		&__add {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			border: 1px dashed #ddd;

			image {
				width: 40rpx;
				height: 36rpx;
				margin-bottom: 10rpx;
			}

			text {
				font-size: 24rpx;
				color: #999;
			}
		}

		&__img {
			width: 220rpx;
			height: 166rpx;
			margin: 7rpx;
			/* 14rpx的一半，因为两边都有 */
			position: relative;
			background-color: #f5f5f5;

			&__preview {
				width: 100%;
				height: 100%;
				border-radius: 8rpx;
			}

			&__delete {
				position: absolute;
				right: -10rpx;
				top: -10rpx;
				width: 30rpx;
				height: 30rpx;
				z-index: 2;
			}
		}
	}
}

/* 校验 */
.verify {
	animation: shake 0.2s ease-in-out 1;

	text:first-child {
		font-weight: bold;
		font-size: 30rpx;
		color: red;
	}
}

@keyframes shake {
	0%,
	100% {
		transform: translateX(0);
	}
	25% {
		transform: translateX(-8px);
	}
	50% {
		transform: translateX(8px);
	}
	75% {
		transform: translateX(-8px);
	}
}

.submit {
	flex: 1;
	margin: 30rpx;
	height: 100rpx;
	min-height: 100rpx;
	margin-top: 15rpx;
	background: #444bf1;
	border-radius: 10rpx;
	font-weight: bold;
	font-size: 32rpx;
	color: #ffffff;
	justify-content: center;
}
</style>

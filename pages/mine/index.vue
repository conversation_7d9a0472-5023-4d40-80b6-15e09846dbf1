<template>
	<view>
		<public-module></public-module>
		<z-paging ref="paging" bg-color="linear-gradient(to bottom,   #E1E1F4 0%, #F1F1F4 30%)" :refresher-only="true" @onRefresh="onRefresh">
			<view slot="top" class="flex nav-wraper">
				<view class="flex u-relative nav-wraper__content" :style="{ height: `${navBarHeight}px`, marginTop: `${statusBarHeight}px` }">
					<!-- #ifdef MP -->
					<view class="flex nav-wraper__content__left">
						<view class="flex nav-wraper__content__left__item" @click="onItmeClick('message')">
							<image class="nav-wraper__content__left__item__ic" :src="iImage('ic_mine_news.png')" mode="aspectFit"></image>
							<view class="animate__animated animate__flash nav-wraper__content__left__item__dot" v-if="badgeData.messageDot"></view>
						</view>
						<view class="flex nav-wraper__content__left__item" @click="onItmeClick('setting')">
							<image class="nav-wraper__content__left__item__ic" :src="iImage('ic_mine_setting.png')" mode="aspectFit"></image>
						</view>
					</view>
					<!-- #endif -->
					<!-- #ifdef APP-PLUS -->
					<view class="flex nav-wraper__content__right">
						<view class="flex nav-wraper__content__right__item" @click="onItmeClick('message')">
							<image class="nav-wraper__content__right__item__ic" :src="iImage('ic_mine_news.png')" mode="aspectFit"></image>
							<view class="animate__animated animate__flash nav-wraper__content__left__item__dot" v-if="badgeData.messageDot"></view>
						</view>
						<view class="flex nav-wraper__content__right__item" @click="onItmeClick('setting')">
							<image class="nav-wraper__content__right__item__ic" :src="iImage('ic_mine_setting.png')" mode="aspectFit"></image>
						</view>
					</view>
					<!-- #endif -->
				</view>
			</view>
			<!-- 用户信息 -->
			<view class="flex user-wrap animate__animated animate__fadeInDown" @click="() => judgeLogin(() => {})">
				<view class="user-wrap__avatar">
					<image :src="uidata.avatar || iImage('img_default_avatar.png')" mode="aspectFill"></image>
				</view>
				<text class="user-wrap__name">{{ uidata.nickName }}</text>
			</view>
			<!-- VIP会员 -->
			<view class="flex vip-wrap" @click="onItmeClick('vip')">
				<image class="vip-wrap__bg" :src="iImage('img_mine_vip_bg.png')"></image>
				<image class="vip-wrap__ic" :src="iImage('ic_mine_vip.png')"></image>
				<view class="flex-col" style="margin-left: 20rpx">
					<text class="vip-wrap__name">{{ uidata.vipName }}</text>
					<text class="vip-wrap__desc">{{ uidata.vipDesc }}</text>
				</view>
				<view class="flex vip-wrap__btn">{{ uidata.vipBtn }}</view>
			</view>
			<!-- 模块 -->
			<view class="flex module-wrap">
				<view class="flex-col module-wrap__item" v-for="(item, index) in moduleItems" :key="index" @click="onItmeClick(item.id)">
					<text class="module-wrap__item__title">{{ item.title }}</text>
					<text class="module-wrap__item__subtitle">{{ item.subtitle }}</text>
					<image class="module-wrap__item__img" :src="iImage(item.icon)" mode="aspectFit"></image>
				</view>
			</view>
			<!-- 列表条目 -->
			<view class="flex list-wrap">
				<view v-if="item.show" class="flex list-wrap__item animate__animated animate__slideInUp" v-for="(item, index) in listItems" :key="index" @click="onItmeClick(item.id)">
					<view class="flex list-wrap__item__top">
						<image class="list-wrap__item__top__ic" :src="iImage(item.icon)" mode="aspectFill"></image>
						<text class="list-wrap__item__top__title">{{ item.name }}</text>
						<view class="flex list-wrap__item__top__right">
							<text v-if="item.status !== undefined" class="list-wrap__item__top__right__open-status">{{ item.status ? '已开启' : '推荐开启' }}</text>
							<view v-if="item.status !== undefined && !item.status" class="list-wrap__item__top__right__dot"></view>
							<image class="list-wrap__item__top__right__ic" src="/static/image/ic_small_arrow_right.png"></image>
						</view>
					</view>
					<view v-if="!$u.test.isEmpty(item.desc)" class="flex list-wrap__item__bottom">
						<text class="list-wrap__item__bottom__desc">{{ item.desc }}</text>
					</view>
				</view>
			</view>
			<!-- 底部TabBar -->
			<view slot="bottom">
				<x-tabbar ref="xTabbar"></x-tabbar>
			</view>
		</z-paging>
		<!-- 客服 -->
		<u-popup :show="kfPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" :overlay-opacity="0.8" @close="kfPopup.show = false">
			<popup-kf @close="kfPopup.show = false"></popup-kf>
		</u-popup>
		<!-- APP下载 -->
		<u-popup :show="appDownloadPopup.show" bgColor="transparent" mode="center" :close-on-click-overlay="false" :overlay-opacity="0.8" @close="appDownloadPopup.show = false">
			<popup-app-download @close="appDownloadPopup.show = false"></popup-app-download>
		</u-popup>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { iWxMiniProgram, openMiniWx, openOtherApp } from '@/common/utils.js';
import { checkPermission } from '@/common/userPermission.js';
export default {
	data() {
		return {
			// 状态栏高度
			statusBarHeight: 0,
			// 导航栏高度
			navBarHeight: 44,
			// 导航栏透明度
			navOpacity: 1,
			// 客服弹窗
			kfPopup: {
				show: false
			},
			// APP下载弹窗
			appDownloadPopup: { 
				show: false
			}
			// Sheet 弹窗
			// actionSheet: {
			// 	show: false,
			// 	title: '请选择',
			// 	options: []
			// }
		};
	},
	computed: {
		...mapState(['userInfo', 'userVip', 'sysConf', 'badgeData']),
		...mapGetters(['getterIsLogin', 'vipStatus']),
		// 界面数据
		uidata() {
			const data = { avatar: undefined, nickName: '点击登录', vipName: '开通会员', vipDesc: '加入会员，尊享服务特权', vipBtn: '开通会员' };
			if (!this.getterIsLogin) {
				return data;
			}
			data.avatar = this.userInfo?.avatar;
			data.nickName = this.userInfo?.nickname;

			if (this.vipStatus?.isValid) {
				const remainingDays = this.vipStatus?.remainingDays;
				if (remainingDays > 0 && remainingDays <= 5) {
					data.vipDesc = `您的会员将会在${remainingDays}天后过期`;
				} else {
					data.vipDesc = `会员有效期：${uni.$u.timeFormat(this.userVip?.endTime, 'yyyy-mm-dd')}`;
				}
				data.vipName = this.userVip?.packageName;
				data.vipBtn = '查看会员';
			}
			return data;
		},
		// 模块项
		moduleItems() {
			return [
				{ id: 'cert', title: '商家入驻', subtitle: '免费查看线索', icon: 'ic_mine_module_enter.png' },
				{ id: 'publish-product', title: '发布服务', subtitle: '在线咨询获客', icon: 'ic_mine_module_publish.png' }
			];
		},
		// 列表条目
		listItems() {
			const isWxBind = this.userInfo?.wechat_bind;

			return [
				{ id: 'customer', show: true, name: '我的客源', icon: 'ic_mine_item_customer.png' },
				{ id: 'mine-publish', show: true, name: '我的发布', icon: 'ic_mine_item_publish.png' },
				{ id: 'helper', show: true, name: '使用教程', icon: 'ic_mine_item_helper.png' },
				{ id: 'weixin-notice', show: true, name: '微信管理', icon: 'ic_mine_item_weixin.png', desc: '在微信上接收新单发布更新等重要信息', status: isWxBind },
				{ id: 'downalod', show: true, name: '财税接单App', icon: 'ic_mine_item_download.png', desc: '一款财税公司专业接单拓客神器，海量精准客户!' },
				{ id: 'adviser', show: true, name: '专属顾问', icon: 'ic_mine_item_adviser.png' }
			];
		}
	},
	onLoad(options) {
		uni.hideTabBar();
		const sys = uni.getSystemInfoSync();
		this.statusBarHeight = sys.statusBarHeight;
	},
	onShow() {
		this.queryData();
		this.$refs.paging && this.$refs.paging.updateFixedLayout();
	},
	methods: {
		...mapActions(['fetchUser', 'fetchUserVip', 'fetchSysConf']),
		// 下拉刷新
		onRefresh() {
			this.queryData();
			setTimeout(() => {
				this.$refs.paging.endRefresh();
			}, 500);
		},
		// 跳转订单页面
		goOrderPage(index) {
			getApp().globalData.orderTabIndex = index;
			this.onJump({ type: 'switchTab', url: '/pages/order/order', login: true });
		},
		// 操作按钮点击
		onItmeClick(id) {
			checkPermission({
				context:this,
				featureType: 'PROVIDER_APPLY_CHECK',
				params: {},
				granted: (result) => {
				
				}
			});
			switch (id) {
				case 'setting':
					// 设置
					this.onJump({ url: '/pagesMine/setting/setting', login: true });
					break;
				case 'message':
					// 消息
					this.onJump({ url: '/pagesChat/conversation/index', login: true });
					break;
				case 'cert':
					// 实名
					this.onJump({ url: '/pagesMine/cert/index', login: true });
					break;
				case 'vip':
					// VIP
					this.onJump({ url: '/pagesMine/vip/index', login: true });
					break;
				case 'customer':
					// 客源
					this.onJump({ url: '/pagesMine/customer/index', login: true });
					break;
				case 'helper':
					// 教程
					this.onJump({ url: '/pagesMine/helper/index', login: true });
					break;
				case 'publish-product':
					// 发布产品
					this.onJump({ url: '/pagesMine/publish/product/publish', login: true });
					break;
				case 'mine-publish':
					// 我的发布
					this.onJump({ url: '/pagesMine/publish/product/list', login: true });
					break;
				case 'wechat-notice':
					// 微信通知
					const item = this.listItems.flat().find((item) => item.id === id);
					if (item && item.status) {
						uni.$u.toast('您已关注公众号，无需再次关注');
					} else {
						this.judgeLogin(() => {
							openMiniWx(iWxMiniProgram('oa', this.$conf.miniProgramId));
						});
					}
					break;
				case 'contact-kf':
					// 联系客服
					this.kfPopup = { show: true };
					break;
				case 'worker-in':
					// 师傅入驻
					this.appDownloadPopup.show = true;
					break;
			}
		},
		// 获取页面数据
		queryData() {
			this.fetchSysConf();
			if (this.getterIsLogin) {
				this.fetchUser();
				this.fetchUserVip();
			}
		}
	}
};
</script>

<style lang="scss" scoped>
/* 导航栏 */
.nav-wraper {
	z-index: 999;
	width: 750rpx;
	background-color: transparent;
	background: transparent;
	&__content {
		flex: 1;
		justify-content: center;
		background-color: transparent;
		background: transparent;
		&__left,
		&__right {
			height: 100%;
			position: absolute;
			top: 0;
			bottom: 0;
			&__item {
				width: 100rpx;
				height: 100%;
				justify-content: center;
				position: relative;
				&__ic {
					width: 44rpx;
					height: 44rpx;
				}
				&__dot {
					width: 16rpx;
					height: 16rpx;
					background: #ff2a39;
					border-radius: 50%;
					position: absolute;
					top: 20rpx;
					left: 70rpx;
				}
			}
		}
		&__left {
			left: 0;
		}
		&__right {
			right: 0;
		}
	}
}

/* 用户信息 */
.user-wrap {
	width: 100%;
	padding: 30rpx;

	&__avatar {
		width: 120rpx;
		height: 120rpx;
		background: #f7f7f7;
		border-radius: 15rpx;
		border: 2px solid #ffffff;
		overflow: hidden;
		image {
			width: 100%;
			height: 100%;
		}
	}
	&__name {
		margin-left: 20rpx;
		font-weight: bold;
		font-size: 40rpx;
		color: #333333;
	}
}

/* VIP会员 */
.vip-wrap {
	height: 140rpx;
	margin: 20rpx 30rpx 0 30rpx;
	padding: 0 20rpx;
	position: relative;

	&__bg {
		z-index: 0;
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
	}

	&__ic {
		z-index: 5;
		width: 125rpx;
		height: 108rpx;
	}

	&__name {
		z-index: 5;
		font-weight: bold;
		font-size: 46rpx;
		color: #fee9cb;
		background: linear-gradient(95deg, #f6cb8e 0%, #ffebcf 36.474609375%, #f6cb8e 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	&__desc {
		z-index: 5;
		font-size: 28rpx;
		color: #fee9cb;
		line-height: 40rpx;
	}

	&__btn {
		z-index: 5;
		width: 160rpx;
		height: 56rpx;
		margin-left: auto;
		background: linear-gradient(98deg, #ecbf7a 0%, #fae1be 73%, #f2c891 100%);
		border-radius: 28rpx;
		justify-content: center;
	}
}

/* 模块 */
.module-wrap {
	flex: 1;
	margin: 20rpx 30rpx 0 30rpx;
	justify-content: space-between;
	&__item {
		width: 336rpx;
		height: 138rpx;
		padding: 0 30rpx;
		background: #ffffff;
		border-radius: 20rpx;
		justify-content: center;
		position: relative;

		&__title {
			font-weight: bold;
			font-size: 34rpx;
			color: #1e2134;
			line-height: 45rpx;
		}

		&__subtitle {
			font-size: 28rpx;
			color: #909099;
			line-height: 45rpx;
		}

		&__img {
			width: 97rpx;
			position: absolute;
			right: 20rpx;
			top: 50%;
			transform: translateY(-50%);
		}
	}
}

/* 条目列表 */
.list-wrap {
	margin: 20rpx 30rpx 30rpx 30rpx;
	background: #ffffff;
	border-radius: 20rpx;
	flex-direction: column;
	align-items: flex-start;
	&__item {
		width: 100%;
		padding: 30rpx;
		flex-direction: column;
		align-items: flex-start;
		&__top {
			width: 100%;
			&__ic {
				width: 44rpx;
				height: 44rpx;
			}
			&__title {
				margin-left: 15rpx;
				font-weight: bold;
				font-size: 30rpx;
				color: #212121;
			}
			&__right {
				margin-left: auto;
				&__open-status {
					width: 100rpx;
					height: 36rpx;
					background: #fff4f5;
					border-radius: 6rpx;
					line-height: 36rpx;
					font-weight: 400;
					font-size: 20rpx;
					color: #ff2a39;
					text-align: center;
				}
				&__dot {
					width: 14rpx;
					height: 14rpx;
					margin: 0 10rpx;
					background: #ff2a39;
					border-radius: 50%;
				}
				&__ic {
					width: 10rpx;
					height: 16rpx;
				}
			}
		}
		&__bottom {
			margin-top: 15rpx;
			padding-left: 60rpx;
			&__desc {
				font-weight: 500;
				font-size: 26rpx;
				color: #999999;
			}
		}
	}
}
</style>

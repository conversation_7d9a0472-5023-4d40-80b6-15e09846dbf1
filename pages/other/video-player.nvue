<template>
	<view class="mask">
		<view class="wrap">
			<view class="video-wrap">
				<video :style="{width:windowWidth+'px',height:windowHeight+'px'}" id="myVideo" :src="videoUrl || ''"
					object-fit="contain" play-btn-position="center" :autoplay="true" enable-play-gesture controls
					@fullscreenchange="playerFullScreen"></video>
			</view>
			<view class="close" :style="{marginTop: statusBarHeight+'px'}" @click="closePage">
				<u-icon name="close" size="33" color="#FFF"></u-icon>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions,
		mapMutations
	} from 'vuex';
	export default {
		data() {
			return {
				// 页面参数【其它页面传递】
				pageParams: {},

				//获取屏幕宽高
				windowWidth: 0,
				//获取屏幕高度
				windowHeight: 0,
				//状态栏高度
				statusBarHeight: 0,

				// 是否显示返回按钮
				showCloseView: true,

				// 视频地址
				videoUrl: '',
			};
		},
		onReady() {
			const sys = uni.getSystemInfoSync();
			this.windowWidth = sys.screenWidth;
			this.windowHeight = sys.windowHeight;
			this.statusBarHeight = sys.statusBarHeight;
		},
		onHide() {
			uni.createVideoContext('myVideo', this).pause();
		},
		onLoad(e) {
			this.pageParams = e;
			if (!uni.$u.test.isEmpty(e.videoUrl)) {
				this.$nextTick(() => {
					this.videoUrl = e.videoUrl;
					uni.createVideoContext('myVideo', this).play(); 
				})
			}
		},
		//方法
		methods: {
			// 全屏监听
			playerFullScreen(e) {
				if (e.detail.fullScreen) {
					//全屏
					this.showCloseView = false
				} else {
					//非全屏			
					this.showCloseView = true
				}
			},
			// 关闭当前页面
			closePage() {
				uni.navigateBack();
			}
		}
	};
</script>
<style lang="scss" scoped>
	.mask {
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.7);
	}

	.wrap {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.close {
		width: 100rpx;
		height: 100rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: fixed;
		top: 0;
		left: 0;
	}
</style>
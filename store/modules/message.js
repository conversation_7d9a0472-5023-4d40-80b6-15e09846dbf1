export const state = {
	// 用户相关红点数据
	badgeData: {
		// 未读红点
		totalDot: 0,
		// 消息了表红点
		messageDot: 0,
		// 订单红点
		order: {
			totalDot: 0,
			pendingDot: 0,
			visitDot: 0,
			serviceDot: 0,
			finishDot: 0
		}
	},
	// 配置相关红点数据
	msgData: {
		// 所有未读的消息
		total: 0,
		// 订单消息数量
		order: {
			total: 0,
			pending: 0,
			visit: 0,
			service: 0,
			finish: 0
		},
	},
};
export const mutations = {
	//储存消息信息
	SET_MSG_DATA(state, data) {
		if (data) {
			state.msgData = uni.$u.deepMerge(state.msgData, data);
			// #ifdef H5
			// window.sessionStorage.setItem('msgData', JSON.stringify(state.msgData));
			// #endif
			// #ifndef H5
			// uni.setStorageSync('msgData', state.msgData);
			// #endif
		}
	},
	//用户相关红点数据
	SET_BADGE_DATA(state, data) {
		if (data) {
			state.badgeData = uni.$u.deepMerge(state.badgeData, data);
			// #ifdef H5
			// window.sessionStorage.setItem('badgeData', JSON.stringify(state.badgeData));
			// #endif
			// #ifndef H5
			// uni.setStorageSync('badgeData', state.badgeData);
			// #endif
		}
	},
};
export const actions = {
	// 更新消息红点
	updateBadge({
		commit,
		state
	}) {
		return new Promise(function(resolve, reject) {
			uni.$u.http
				.post('/user/getHongNumberList', {}, {
					custom: {
						toast: false
					}
				})
				.then((res) => {
					// 递归计算总点数的函数
					const calculateTotalDots = (obj) => {
						let totalDot = 0;
						const result = {
							...obj
						};
						// 遍历对象的每一个键
						for (const key in obj) {
							if (typeof obj[key] === 'object' && obj[key] !== null) {
								// 递归计算子对象的总点数
								const childResult = calculateTotalDots(obj[key]);
								totalDot += childResult.totalDot; // 累加子对象的总点数
								result[key] = childResult; // 更新当前层级的对象
							} else {
								// 如果是数值，累加到总点数
								totalDot += obj[key] || 0;
							}
						}
						// 在结果对象中设置总点数
						result.totalDot = totalDot;
						return result;
					};
					const msgDot = {
						messageDot: res?.message_count || 0,
						order: {
							pendingDot: res?.product_wait_count_red || 0,
							visitDot: res?.product_running_count_red || 0,
							serviceDot: res?.product_doing_count_red || 0,
							finishDot: res?.product_finish_count_red || 0
						}
					};
					const newMsgDots = calculateTotalDots(msgDot)
					commit('SET_BADGE_DATA', newMsgDots);
					commit('SET_TABBAR_BADGE', { // 订单Tab
						index: 2,
						count: newMsgDots.order?.totalDot || 0
					});
					resolve()
				})
				.catch((err) => {
					reject()
				});
		});
	}
};
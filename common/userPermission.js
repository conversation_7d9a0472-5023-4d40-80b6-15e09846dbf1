import store from '@/store';
import constant from '@/common/constant.js';
import {
	judgeLogin
} from '@/common/login';
/**
 * 检测操作权限
 * @param {Object} 检测的必要参数, 
 * @param {CallBack} 已授权的回调 
 * @param {CallBack} 已拒绝的回调 
 * @param {CallBack} 失败的回调 
 */
export const checkPermission = ({
	context = null,
	featureType,
	params = {},
	granted = null,
	denied = null,
	fail = null
}) => {
	judgeLogin(() => {
		const reqData = {
			featureType: featureType,
			params: params
		};
		// 1、权限检测
		uni.$u.http.post('/biz/permission/check', reqData).then(res => {
			// 权限检测通过
			console.log("用户操作权限检测通过-----------", res)
			if (res && res.hasPermission) {
				// ✅ 权限检测通过
				console.log("用户操作权限检测通过:", res);
				granted && granted(res);
			} else {
				// ❌ 权限检测拒绝
				console.log("用户操作权限检测拒绝:", res);
				denied && denied(handlePermissionDenied(context, res));
			}
		}).catch(res => {
			console.log("用户操作权限检测拒绝-----------", res)
			denied && denied(handlePermissionDenied(context, res));
		})
	})
}

/**
 * 处理权限结果
 * 
 * @param {Object} context 上下文
 * @param {Object} res
 */
export const handlePermissionDenied = (context, res) => {
	const {
		errorCode,
		errorMessage,
		requiredAction,
		actionData
	} = res;
	const data = {
		type: 'popup',
		popupName: 'promptPopup',
		popupOptions: {}
	}
	switch (String(res.code)) {
		case cons.PERMISSION_RESULT_DEPOSIT_REQUIRED: // 需要缴纳保证金
		case cons.PERMISSION_RESULT_CREDIT_TOO_LOW_NEED_DEPOSIT: // 信用值过低【需要缴纳保障金】
			var title = '缴纳保证金'
			var msg = `<span style='color:#999999;'>保证金（可退）</span><br/> ${res.msg || '缴纳保证金后可上门服务。'}`
			if (String(res.code) == cons.PERMISSION_RESULT_CREDIT_TOO_LOW_NEED_DEPOSIT) {
				title = '信用值不足'
				msg = res.msg || `您的信用值不足，<br/>须缴纳保障金后继续抢单，<br>缴纳后信用值恢复到10。`
			}
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				icon: '/static/image/ic_alert_deposit.png',
				iconStyle: 'width:170rpx;height:170rpx;margin-top:-120rpx;',
				title: title,
				message: msg,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'deposit',
					title: '立即缴纳',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
						context.onJump({
							url: '/pagesMine/deposit/deposit'
						});
					}
				}],
				showClose: true
			};
			break
		case cons.PERMISSION_RESULT_ACTIVE_ORDER_LIMIT_EXCEEDED:
			// 进行中的单子超过最大限制，需要去完成未完成的单子
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '温馨提示',
				message: res.msg || `您有3个未完成的订单，<br/>至少完成一个订单可继续抢单！`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'do-order',
					title: '去完成',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
						context.onJump({
							type: 'switchTab',
							url: '/pages/order/order'
						});
					}
				}],
				showClose: true
			};
			break
		case cons.PERMISSION_RESULT_VIP_REQUIRED:
		case cons.PERMISSION_RESULT_VIP_REQUIRED2:
			// 需要开通会员
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				icon: '/static/image/ic_alert_vip.png',
				iconStyle: 'width:170rpx;height:170rpx;margin-top:-120rpx;',
				title: '开通会员',
				message: res.msg || `请先开通会员后抢单哦！`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'open-vip',
					title: '立即开通',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						console.log("开通会员-----")
						context.promptPopup.show = false;
						context.onJump({
							url: '/pagesMine/member/member-center'
						});
					}
				}],
				showClose: true
			}
			break;
		case cons.PERMISSION_RESULT_ORDER_FINISHED:
			// 该单已完成
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '温馨提示',
				message: res.msg || `该单已完成，<br/>请查看其它服务需求。`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'cancel',
					title: '知道了',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
					}
				}],
				showClose: false
			};
			break
		case cons.PERMISSION_RESULT_ORDER_EXPIRED:
			// 该单已过期
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '温馨提示',
				message: res.msg || `该单已过期，<br/>请查看其它服务需求。`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'cancel',
					title: '知道了',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
					}
				}],
				showClose: false
			};
			break
		case cons.PERMISSION_RESULT_ORDER_TAKEN:
			// 该单已被他人抢走
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '温馨提示',
				message: res.msg || `您慢了一步，该单已被抢，<br/>请查看其它服务需求。`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'cancel',
					title: '知道了',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
					}
				}],
				showClose: false
			};
			break
		case cons.PERMISSION_RESULT_CREDIT_TOO_LOW_WAIT_RECOVERY:
			// 信用值过低【等待恢复信用值】
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '温馨提示',
				message: res.msg || `您的信用值不足，<br/>无法接单，您需等待15天后，<br>信用值恢复到-，再来抢单。`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'cancel',
					title: '知道了',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
					}
				}],
				showClose: false
			};
			break
		case cons.PERMISSION_RESULT_DEPOSIT_REFUNDING:
			// 保证金退还中
			data.popupOptions = {
				show: true,
				icon: '/static/image/ic_alert_wait.png',
				title: '保证金退还中',
				message: res.msg ||
					`保证金原路退回中<br/>剩余退回到账时间: <span style="color:#FC1800;font-weight:bold;">-</span><br/>您本次抢单<br/>保证金退回申请将自动取消`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
						type: 'cancel',
						title: '取消',
						customStyle: 'width:220rpx;height:90rpx;border-radius: 30rpx;border: 0.8px solid #575CE5;color:#575CE5;',
						action: () => {
							context.promptPopup.show = false;
						}
					},
					{
						type: 'continue-view',
						title: '确定',
						customStyle: 'width:220rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
						action: () => {
							context.promptPopup.show = false;
						}
					}
				],
				showClose: false
			};
			break
		case cons.PERMISSION_RESULT_VIP_PRIORITY:
			// 需要开通会员【会员优先抢单中】
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				icon: '/static/image/ic_alert_vip.png',
				iconStyle: 'width:170rpx;height:170rpx;margin-top:-120rpx;',
				title: '开通会员',
				message: res.msg || `会员抢单中...<br/>会员优先10-15分钟抢单，<br/>请稍后再来查看`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
						type: 'cancel',
						title: '稍后再来',
						customStyle: 'width:220rpx;height:90rpx;border-radius: 30rpx;border: 0.8px solid #575CE5;color:#575CE5;',
						action: () => {
							context.promptPopup.show = false;
						}
					},
					{
						type: 'open-vip',
						title: '开通接单',
						customStyle: 'width:220rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
						action: () => {
							context.promptPopup.show = false;
							context.onJump({
								url: '/pagesMine/member/member-center'
							});
						}
					}
				],
				showClose: false
			}
			break
		case cons.PERMISSION_RESULT_VIP_EXPIRED:
			// 需要开通会员【会员已过期】
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				icon: '/static/image/ic_alert_fail.png',
				title: '会员已过期',
				message: res.msg || `您的会员已过期，续费后<br/>可继续抢单`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'open-vip',
					title: '立即续费',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
						context.onJump({
							url: '/pagesMine/member/member-center'
						});
					}
				}],
				showClose: true
			}
			break
		case cons.PERMISSION_RESULT_REAL_AUTH_REQUIRED:
			// 需要实名认证
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '商家认证',
				message: res.msg || `商家认证后即可抢单！`,
				backgroundImage: '/static/image/img_dialog_auth_bg.png',
				customStyle: {
					width: '560rpx',
					height: '710rpx',
					justifyContent: 'flex-end'
				},
				actions: [{
						type: 'auth',
						title: '立即认证',
						customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
						action: () => {
							context.promptPopup.show = false;
							context.onJump({
								url: '/pagesMine/auth/real-auth'
							});
						}
					},
					{
						type: 'cancel',
						title: '下次再说',
						customStyle: 'width:480rpx;margin-top:15rpx;border-radius: 30rpx;color:#999999;',
						action: () => {
							context.promptPopup.show = false;
						}
					}
				],
				showClose: false
			};
			break

		case cons.PERMISSION_RESULT_REAL_AUTH_REVIEW:
			// 实名认证审核中
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				icon: '/static/image/ic_alert_wait.png',
				title: '认证审核中',
				message: res.msg || `您的认证信息已提交审核，1~2工作日<br/>审核完成，请耐心等待。<br/>审核通过后上门服务！`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'cancel',
					title: '知道了',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
					}
				}],
				showClose: false
			}
			break
		case cons.PERMISSION_RESULT_REAL_AUTH_REJECTED:
			// 实名认证被驳回
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				icon: '/static/image/ic_alert_fail.png',
				title: '认证失败',
				message: res.msg || `您的认证信息已被驳回<br/>请重新提交`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'auth',
					title: '重新认证',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
						context.onJump({
							url: '/pagesMine/auth/real-auth'
						});
					}
				}],
				showClose: true
			}
			break
		case cons.PERMISSION_RESULT_ORDER_CATEGORY_MISMATCH:
			// 订单类目和认证类目不符合
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '认证类目不符',
				message: res.msg || `您认证服务类目与此单类型不符，<br/>请查看相关服务需求。`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'cancel',
					title: '知道了',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
					}
				}],
				showClose: false
			}
			break
		case cons.PERMISSION_RESULT_ORDER_AREA_MISMATCH:
			// 订单区域和认证区域不符合
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '认证区域不符',
				message: res.msg || `您认证服务类目与此单类型不符，<br/>请查看相关服务需求。`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'cancel',
					title: '知道了',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
					}
				}],
				showClose: false
			}
			break
		case cons.PERMISSION_RESULT_UNPAID_ORDER:
			// 有未完成的单子【退回保证金操作时提示】
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '温馨提示',
				message: res.msg || `您当前有待完成订单，<br/>请处理完成后再申请退回保证金！`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'do-order',
					title: '去完成',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.onJump({
							type: 'switchTab',
							url: '/pages/order/order'
						});
					}
				}],
				showClose: true
			}
			break
		case cons.PERMISSION_RESULT_ALREADY_TAKEN:
			// 已经接过这个单子
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '温馨提示',
				message: res.msg || `该单您已接过不能再次接单，<br/>请查看其他服务需求`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'cancel',
					title: '知道了',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
					}
				}],
				showClose: false
			}
			break
		case cons.PERMISSION_RESULT_UNFINISHED_ORDER_OVER_72_HOURS:
			// 您有超过72小时未完成的订单
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				title: '温馨提示',
				message: res.msg || `您有超过72小时未完成的订单，<br/>请完成订单后再抢单`,
				customStyle: {
					backgroundColor: '#FFFFFF'
				},
				actions: [{
					type: 'do-order',
					title: '去完成',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
						context.onJump({
							type: 'switchTab',
							url: '/pages/order/order'
						});
					}
				}],
				showClose: true
			}
			break
		case cons.PERMISSION_RESULT_OFFICIAL_ACCOUNT:
			// 关注公众号
			data.popupName = 'officialAccountPopup';
			data.popupOptions = {
				show: true
			}
			break
		case cons.PERMISSION_RESULT_SIGN_AGREEMENT:
			// 签署协议
			data.popupOptions = {
				show: true,
				ident: 'prompt',
				icon: '/static/image/ic_alert_signagreement.png',
				iconStyle: 'width:168rpx;height:199rpx;',
				title: '未签署协议',
				titleStyle: 'color:red;',
				message: res.msg || `还差最后一步，请入驻本人<br/>在线完成协议签署，<br/>即可开始抢单！`,
				tipText: '*签署协议信息要与入驻身份证信息一致',
				customStyle: {
					background: 'linear-gradient(0deg, #FFFFFF 0%, #ECEDFF 100%)'
				},
				actions: [{
					type: 'sign-agreement',
					title: '立即签署',
					customStyle: 'width:480rpx;height:90rpx;border-radius: 30rpx;background: #575CE5;color:#FFFFFF;',
					action: () => {
						context.promptPopup.show = false;
						context.onJump({
							url: '/pagesMine/auth/real-auth',
							login: true
						})
					}
				}],
				showClose: true
			}
			break
		default:
			uni.$u.toast(res.msg || '操作异常', 5000);
			break
	}
	return data
}